/* Basic reset and Ant Design compatibility */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family:
    'Diodrum',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  background-color: #ffffff;
  color: #000000;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Ant Design Input customization - now handled by ConfigProvider theme */
/* Most input styling is now handled by the ConfigProvider theme */
/* Keep only specific overrides that aren't covered by the theme */

.ant-input:focus,
.ant-input-focused {
  border-color: var(--brand-500) !important;
  box-shadow: none !important;
}

/* Specific styling for parameter input classes */
.parameter-input.ant-input.normalized {
  background: #f8f8f8 !important;
  color: #667085 !important;
}

.parameter-input.ant-input.threshold {
  background: #fcfcfd !important;
}

.parameter-input.ant-input.threshold:disabled {
  background: #f8f8f8 !important;
}

/* Ensure proper layout flow */
.ro-advisor {
  display: block !important;
}

.main-content {
  display: flex !important;
  flex-direction: column !important;
}
