/**
 * Form validation utilities
 * Handles validation for form inputs and complete form data
 */

import {
  validateWithRules,
  createValidationRule,
  isEmpty
} from './validationCore';
import { CIP_CONFIG } from '../../constants';

/**
 * Form field types
 */
export const FORM_FIELD_TYPES = {
  TEXT: 'text',
  NUMBER: 'number',
  DATE: 'date',
  EMAIL: 'email',
  SELECT: 'select',
  CHECKBOX: 'checkbox'
};

/**
 * Common validation rule presets
 */
export const VALIDATION_PRESETS = {
  REQUIRED_TEXT: [
    createValidationRule('required')
  ],
  REQUIRED_NUMBER: [
    createValidationRule('required'),
    createValidationRule('number')
  ],
  REQUIRED_DATE: [
    createValidationRule('required'),
    createValidationRule('date')
  ],
  OPTIONAL_NUMBER: [
    createValidationRule('number')
  ],
  OPTIONAL_DATE: [
    createValidationRule('date')
  ],
  EMAIL: [
    createValidationRule('email')
  ],
  REQUIRED_EMAIL: [
    createValidationRule('required'),
    createValidationRule('email')
  ]
};

/**
 * Form field validation configuration
 * @typedef {Object} FormFieldConfig
 * @property {string} type - Field type
 * @property {boolean} required - Whether field is required
 * @property {Array} rules - Validation rules
 * @property {string} label - Field label for error messages
 */

/**
 * Validate a form field
 * @param {any} value - Field value
 * @param {FormFieldConfig} config - Field configuration
 * @returns {ValidationResult} Validation result
 */
export const validateFormField = (value, config) => {
  const { type, required = false, rules = [], label = 'Field' } = config;

  // Create default rules based on field type
  const defaultRules = [];

  if (required) {
    defaultRules.push(createValidationRule('required'));
  }

  switch (type) {
    case FORM_FIELD_TYPES.NUMBER:
      defaultRules.push(createValidationRule('number'));
      break;
    case FORM_FIELD_TYPES.DATE:
      defaultRules.push(createValidationRule('date'));
      break;
    case FORM_FIELD_TYPES.EMAIL:
      defaultRules.push(createValidationRule('email'));
      break;
  }

  // Combine default rules with custom rules
  const allRules = [...defaultRules, ...rules];

  return validateWithRules(value, allRules);
};

/**
 * Validate numeric input with min/max constraints
 * @param {any} value - Value to validate
 * @param {Object} constraints - Validation constraints
 * @param {number} constraints.min - Minimum value
 * @param {number} constraints.max - Maximum value
 * @param {boolean} constraints.required - Whether field is required
 * @param {string} constraints.unit - Unit for error messages
 * @returns {ValidationResult} Validation result
 */
export const validateNumericInput = (value, constraints = {}) => {
  const { min, max, required = false, unit = '' } = constraints;

  const rules = [];

  if (required) {
    rules.push(createValidationRule('required'));
  }

  rules.push(createValidationRule('number'));

  if (min !== undefined || max !== undefined) {
    rules.push(createValidationRule('range', { min, max, unit }));
  }

  return validateWithRules(value, rules);
};

/**
 * Validate date input
 * @param {string} value - Date string
 * @param {Object} options - Validation options
 * @param {boolean} options.required - Whether field is required
 * @param {string} options.format - Expected date format
 * @returns {ValidationResult} Validation result
 */
export const validateDateInput = (value, options = {}) => {
  const { required = false, format = 'MM/DD/YYYY' } = options;

  const rules = [];

  if (required) {
    rules.push(createValidationRule('required'));
  }

  rules.push(createValidationRule('date', { format }));

  return validateWithRules(value, rules);
};

/**
 * Validate CIP frequency
 * @param {any} value - Frequency value
 * @returns {ValidationResult} Validation result
 */
export const validateCIPFrequency = (value) => {
  return validateNumericInput(value, {
    min: CIP_CONFIG.FREQUENCY_MIN,
    max: CIP_CONFIG.FREQUENCY_MAX,
    required: true,
    unit: 'per year'
  });
};

/**
 * Validate CIP duration
 * @param {any} value - Duration value
 * @returns {ValidationResult} Validation result
 */
export const validateCIPDuration = (value) => {
  if (isEmpty(value)) {
    return {
      isValid: false,
      error: 'Duration is required',
      value
    };
  }

  return { isValid: true, error: '', value };
};

/**
 * Validate temperature input
 * @param {any} value - Temperature value
 * @param {Object} constraints - Temperature constraints
 * @returns {ValidationResult} Validation result
 */
export const validateTemperature = (value, constraints = {}) => {
  const { min = -50, max = 100, required = true, unit = '°C' } = constraints;

  return validateNumericInput(value, { min, max, required, unit });
};

/**
 * Validate flow rate input
 * @param {any} value - Flow rate value
 * @param {Object} constraints - Flow rate constraints
 * @returns {ValidationResult} Validation result
 */
export const validateFlowRate = (value, constraints = {}) => {
  const { min = 0, max = 240000, required = true, unit = 'm³/d' } = constraints;

  return validateNumericInput(value, { min, max, required, unit });
};

/**
 * Validate conductivity input
 * @param {any} value - Conductivity value
 * @param {Object} constraints - Conductivity constraints
 * @returns {ValidationResult} Validation result
 */
export const validateConductivity = (value, constraints = {}) => {
  const { min = 0, max = 5000, required = true, unit = 'μS/cm' } = constraints;

  return validateNumericInput(value, { min, max, required, unit });
};

/**
 * Validate complete form data
 * @param {Object} formData - Form data object
 * @param {Object} validationSchema - Validation schema with field configurations
 * @returns {Object} Validation results for each field
 */
export const validateFormData = (formData, validationSchema) => {
  const results = {};

  Object.entries(validationSchema).forEach(([fieldPath, config]) => {
    const value = getNestedValue(formData, fieldPath);
    results[fieldPath] = validateFormField(value, config);
  });

  return results;
};

/**
 * Check if form data is valid
 * @param {Object} validationResults - Results from validateFormData
 * @returns {boolean} True if all fields are valid
 */
export const isFormValid = (validationResults) => {
  return Object.values(validationResults).every(result => result.isValid);
};

/**
 * Get form validation errors
 * @param {Object} validationResults - Results from validateFormData
 * @returns {Object} Object with field paths as keys and error messages as values
 */
export const getFormValidationErrors = (validationResults) => {
  const errors = {};

  Object.entries(validationResults).forEach(([fieldPath, result]) => {
    if (!result.isValid && result.error) {
      errors[fieldPath] = result.error;
    }
  });

  return errors;
};

/**
 * Get nested value from object using dot notation
 * @param {Object} obj - Object to get value from
 * @param {string} path - Dot notation path (e.g., 'start.temperature')
 * @returns {any} Value at path or undefined
 */
export const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

/**
 * Set nested value in object using dot notation
 * @param {Object} obj - Object to set value in
 * @param {string} path - Dot notation path
 * @param {any} value - Value to set
 * @returns {Object} Updated object
 */
export const setNestedValue = (obj, path, value) => {
  const keys = path.split('.');
  const lastKey = keys.pop();

  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);

  target[lastKey] = value;
  return obj;
};

/**
 * Create validation schema for form sections
 * @param {Object} sectionConfig - Configuration for each section
 * @returns {Object} Complete validation schema
 */
export const createFormValidationSchema = (sectionConfig) => {
  const schema = {};

  Object.entries(sectionConfig).forEach(([section, fields]) => {
    Object.entries(fields).forEach(([field, config]) => {
      const fieldPath = `${section}.${field}`;
      schema[fieldPath] = config;
    });
  });

  return schema;
};

/**
 * Common form validation schemas
 */
export const COMMON_VALIDATION_SCHEMAS = {
  FORM_SECTION: {
    date: {
      type: FORM_FIELD_TYPES.DATE,
      required: true,
      label: 'Date'
    },
    temperature: {
      type: FORM_FIELD_TYPES.NUMBER,
      required: true,
      label: 'Temperature',
      rules: [createValidationRule('range', { min: -50, max: 100, unit: '°C' })]
    },
    feedFlow: {
      type: FORM_FIELD_TYPES.NUMBER,
      required: true,
      label: 'Feed Flow',
      rules: [createValidationRule('range', { min: 0, max: 240000, unit: 'm³/d' })]
    },
    feedConductivity: {
      type: FORM_FIELD_TYPES.NUMBER,
      required: true,

      label: 'Feed Conductivity',
      rules: [createValidationRule('range', { min: 0, max: 5000, unit: 'μS/cm' })]
    }
  },
  CIP_SECTION: {
    frequency: {
      type: FORM_FIELD_TYPES.NUMBER,
      required: true,
      label: 'CIP Frequency',
      rules: [createValidationRule('range', {
        min: CIP_CONFIG.FREQUENCY_MIN,
        max: CIP_CONFIG.FREQUENCY_MAX,
        unit: 'per year'
      })]
    },
    duration: {
      type: FORM_FIELD_TYPES.TEXT,
      required: true,
      label: 'CIP Duration'
    }
  }
};
