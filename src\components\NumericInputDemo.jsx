import { Form, Button, Card, Radio, message } from 'antd';
import { useState } from 'react';
import NumericInput from './NumericInput';
import { FormProvider } from '../contexts/FormProvider';
import { UNIT_SYSTEMS } from '../utils/unitConversion';

/**
 * Demo component to test the updated NumericInput with Form.Item validation
 */
export default function NumericInputDemo() {
  const [form] = Form.useForm();
  const [unitSystem, setUnitSystem] = useState(UNIT_SYSTEMS.METRIC);

  const handleSubmit = async (values) => {
    try {
      console.log('Form values:', values);
      message.success('Form submitted successfully!');
    } catch (error) {
      message.error('Form submission failed');
    }
  };

  const handleValidate = async () => {
    try {
      const values = await form.validateFields();
      console.log('Validation passed:', values);
      message.success('All fields are valid!');
    } catch (errorInfo) {
      console.log('Validation failed:', errorInfo);
      message.error('Please fix the validation errors');
    }
  };

  const handleUnitSystemChange = (e) => {
    setUnitSystem(e.target.value);
    // Clear validation when unit system changes
    form.clearValidate();
    message.info(`Unit system changed to ${e.target.value}`);
  };

  return (
    <FormProvider initialUnitSystem={unitSystem}>
      <div style={{ padding: '24px', maxWidth: '800px' }}>
        <h2>NumericInput Form.Item Demo</h2>
        
        <Card title="Unit System Selection" style={{ marginBottom: '24px' }}>
          <Radio.Group value={unitSystem} onChange={handleUnitSystemChange}>
            <Radio.Button value={UNIT_SYSTEMS.METRIC}>Metric</Radio.Button>
            <Radio.Button value={UNIT_SYSTEMS.US}>US</Radio.Button>
            <Radio.Button value={UNIT_SYSTEMS.CUSTOM}>Custom</Radio.Button>
          </Radio.Group>
          <p style={{ marginTop: '16px' }}>
            Current Unit System: <strong>{unitSystem}</strong>
          </p>
        </Card>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '24px' }}
        >
          <Card title="Static Validation (Props-based)" style={{ marginBottom: '24px' }}>
            <NumericInput
              name="temperature"
              label="Temperature"
              unit="°C"
              min={-50}
              max={100}
              required
              placeholder="Enter temperature"
            />
            
            <NumericInput
              name="pressure"
              label="Static Pressure"
              unit="bar"
              min={0}
              max={50}
              required
              placeholder="Enter pressure"
            />
          </Card>

          <Card title="Dynamic Validation (Parameter-based)" style={{ marginBottom: '24px' }}>
            <p>These inputs use dynamic validation that changes based on the unit system:</p>
            
            <NumericInput
              name={['parameters', 'feedPressure', 'start']}
              label="Feed Pressure (Start)"
              parameterType="feedPressure"
              fieldName="start"
              required
              placeholder="Enter start pressure"
            />
            
            <NumericInput
              name={['parameters', 'feedPressure', 'current']}
              label="Feed Pressure (Current)"
              parameterType="feedPressure"
              fieldName="current"
              required
              placeholder="Enter current pressure"
            />
            
            <NumericInput
              name={['parameters', 'qPerm', 'start']}
              label="Flow Rate (Start)"
              parameterType="qPerm"
              fieldName="start"
              required
              placeholder="Enter start flow rate"
            />
            
            <NumericInput
              name={['parameters', 'condPerm', 'start']}
              label="Conductivity (Start)"
              parameterType="condPerm"
              fieldName="start"
              required
              placeholder="Enter start conductivity"
            />
          </Card>

          <Card title="Read-only Fields" style={{ marginBottom: '24px' }}>
            <NumericInput
              name={['parameters', 'feedPressure', 'normalized']}
              label="Feed Pressure (Normalized)"
              parameterType="feedPressure"
              fieldName="normalized"
              isReadOnly
              placeholder="Calculated value"
            />
          </Card>

          <div style={{ marginTop: '24px' }}>
            <Button type="primary" htmlType="submit" style={{ marginRight: '16px' }}>
              Submit Form
            </Button>
            <Button onClick={handleValidate}>
              Validate Fields
            </Button>
          </div>
        </Form>

        <Card title="How It Works" style={{ marginTop: '24px' }}>
          <h4>Static Validation</h4>
          <p>Uses min/max props directly for validation constraints.</p>
          
          <h4>Dynamic Validation</h4>
          <p>Uses parameterType and fieldName props to get validation constraints from parameter configuration.</p>
          <p>Constraints automatically update when unit system changes:</p>
          <ul>
            <li><strong>Metric:</strong> Pressure 0-100 bar, Flow 0-500 m³/h</li>
            <li><strong>US:</strong> Pressure 0-1450 psi, Flow 0-2201 gpm</li>
            <li><strong>Custom:</strong> Dynamically converted based on selected units</li>
          </ul>
          
          <h4>Benefits</h4>
          <ul>
            <li>✅ Simplified validation logic using Form.Item</li>
            <li>✅ Automatic error handling by Ant Design</li>
            <li>✅ Dynamic unit conversion support</li>
            <li>✅ Clear help text showing constraints</li>
            <li>✅ Easy to debug and maintain</li>
          </ul>
        </Card>
      </div>
    </FormProvider>
  );
}
