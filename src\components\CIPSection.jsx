import PropTypes from 'prop-types';
import NumericInput from './NumericInput';
import { CIP_CONFIG, UNITS_CONFIG } from '../constants';

export default function CIPSection({
  frequency,
  duration,
  onFrequencyChange,
  onDurationChange
}) {
  return (
    <div className="grid-column">
      <h3>CIP Settings</h3>

      <NumericInput
        label={CIP_CONFIG.FREQUENCY_LABEL}
        value={frequency}
        onChange={onFrequencyChange}
        unit={UNITS_CONFIG.cipFrequency}
        min={CIP_CONFIG.FREQUENCY_MIN}
        max={CIP_CONFIG.FREQUENCY_MAX}
        step={1}
      />

      <NumericInput
        label={CIP_CONFIG.DURATION_LABEL}
        value={duration}
        onChange={onDurationChange}
        unit={UNITS_CONFIG.cipDuration}
        placeholder={CIP_CONFIG.DURATION_PLACEHOLDER}
      />
    </div>
  );
}

CIPSection.propTypes = {
  frequency: PropTypes.number.isRequired,
  duration: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onFrequencyChange: PropTypes.func.isRequired,
  onDurationChange: PropTypes.func.isRequired
};
