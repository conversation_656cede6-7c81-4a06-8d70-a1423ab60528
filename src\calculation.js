// Normalized Feed Pressure (NFP)
// NFP=E14/L27+E13/2+(0.0385 * (E10*0.5*(LN(1 / (1 - (E14 / E9))) / (E14 / E9))) * (E8 + 273.15) / (1000 - ((E10*0.5*(LN(1 / (1 - (E14 / E9))) / (E14 / E9))) / 1000)) / 14.5)

//Normalized Flow Rate (NFR)
// NFR=E13*POWER(AVERAGE(D9,D14)/AVERAGE(E9,E14),2-0.6)*POWER((EXP(-3.7188+(578.919/(-137.546+273+D8)))/1000)/(EXP(-3.7188+(578.919/(-137.546+273+E8)))/1000),0.6)


//Normalized Qperm (NQPerm)
// NQPerm=E14*(EXP(2640*((1/298.15)-1/(D8+273.15))))/(EXP(2640*((1/298.15)-1/(E8+273.15))))

//Normalized Cperm (NCPerm)
// NCPerm=E15*(EXP(2640*((1/298.15)-1/(D8+273.15))))/(EXP(2640*((1/298.15)-1/(E8+273.15))))


// Where:

// Feed Pressure (E14) = The pressure of the feed water entering the system.
// Flow Rate (L27) = The rate at which the feed water flows through the system.
// Pressure Drop (E13) = The pressure difference between the feed and brine streams.
// Feed Conductivity (E10) = The conductivity of the feed water, which is related to its salinity.
// Initial Pressure (E9) = The initial pressure of the feed water when the system started.
// Temperature (E8) = The temperature of the feed water.
//Initial Temperature (D8) = The initial temperature of the feed water.
// Current Temperature (E8) = The current temperature of the feed water./
// initial feed flow(D9)
// initial QPerm (D14)
// current QPerm (E14)
//normalized feed pressure (F12)
//feed pressure start (D12)
//normalized flow rate (F13)
//flow rate start (D13)
//normalized Qperm (F14)
//Qperm start (D14)
//normalized Cperm (F15)
//Cperm start (D15)

// export const 

export const calculateNFP = (feedPressure, flowRate, pressureDrop, feedConductivity, initialPressure, temperature) => {
  const nfp = (feedPressure / flowRate) + (feedPressure / 2) + 
    (0.0385 * ((feedConductivity * 0.5 * Math.log(1 / (1 - (feedPressure / initialPressure))))/(feedPressure / initialPressure)) * 
    (temperature + 273.15) / 
    (1000 - ((feedConductivity * 0.5 * Math.log(1 / (1 - (feedPressure / initialPressure)))/(feedPressure / initialPressure)) / 1000)) / 
    14.5);
  
  return nfp;
}

export const calculateNFR = (flowRate, initialQPerm, currentQPerm, initialTemperature, currentTemperature) => {
  const nfr = flowRate * Math.pow((initialQPerm / currentQPerm), 2 - 0.6) * 
    Math.pow((Math.exp(-3.7188 + (578.919 / (-137.546 + 273 + initialTemperature))) / 1000) / 
    (Math.exp(-3.7188 + (578.919 / (-137.546 + 273 + currentTemperature))) / 1000), 0.6);
  
  return nfr;
}


export const calculateNQPerm = (currentQPerm, initialTemperature, currentTemperature) => {
  const nQPerm = currentQPerm * 
    (Math.exp(2640 * ((1 / 298.15) - (1 / (initialTemperature + 273.15))))) / 
    (Math.exp(2640 * ((1 / 298.15) - (1 / (currentTemperature + 273.15)))));
  
  return nQPerm;
}

export const calculateNCondPerm = (currentCPerm, initialTemperature, currentTemperature) => {
  const nCPerm = currentCPerm * 
    (Math.exp(2640 * ((1 / 298.15) - (1 / (initialTemperature + 273.15))))) / 
    (Math.exp(2640 * ((1 / 298.15) - (1 / (currentTemperature + 273.15)))));
  
  return nCPerm;
}

//condtion calculation
// condition_fp(I12) =ABS((F12-D12)/D12)
//condition_dp = =ABS((F13-D13)/D13)
//Green(J12)=10%
//yellow()
//if I12<Green(J12) then "Normal" (greencolor)


