import { STATUS_COLORS, STATUS_TEXT, STATUS_ICONS, STATUS_TYPES } from '../constants';

/**
 * Get color for a given status
 * @param {string} status - The status type
 * @returns {string} Color value
 */
export const getStatusColor = (status) => {
  return STATUS_COLORS[status] || STATUS_COLORS.default;
};

/**
 * Get text for a given status
 * @param {string} status - The status type
 * @returns {string} Status text
 */
export const getStatusText = (status) => {
  return STATUS_TEXT[status] || STATUS_TEXT.default;
};

/**
 * Get icon for a given status
 * @param {string} status - The status type
 * @returns {string} Status icon
 */
export const getStatusIcon = (status) => {
  return STATUS_ICONS[status] || STATUS_ICONS.default;
};

/**
 * Validate if a status is valid
 * @param {string} status - The status to validate
 * @returns {boolean} True if valid status
 */
export const isValidStatus = (status) => {
  return Object.values(STATUS_TYPES).includes(status);
};

/**
 * Get status configuration object
 * @param {string} status - The status type
 * @returns {Object} Status configuration with color, text, and icon
 */
export const getStatusConfig = (status) => {
  return {
    color: getStatusColor(status),
    text: getStatusText(status),
    icon: getStatusIcon(status),
    isValid: isValidStatus(status)
  };
};
