import { useState } from 'react';
import OptionSelector from '../OptionSelector';

/**
 * Example usage of OptionSelector component
 * Demonstrates different configurations and use cases
 */
export default function OptionSelectorExample() {
  const [selectedUnit, setSelectedUnit] = useState('bar');
  const [selectedSize, setSelectedSize] = useState('medium');
  const [selectedStatus, setSelectedStatus] = useState('active');

  // Example options for different use cases
  const unitOptions = [
    { value: 'psi', label: 'Psi' },
    { value: 'bar', label: 'bar' }
  ];

  const sizeOptions = [
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' }
  ];

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'pending', label: 'Pending' }
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '600px' }}>
      <h2>OptionSelector Component Examples</h2>
      
      {/* Basic Usage - Unit Selector */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Unit Selector (Medium Size)</h3>
        <OptionSelector
          options={unitOptions}
          selectedOption={selectedUnit}
          onSelect={setSelectedUnit}
        />
        <p>Selected: {selectedUnit}</p>
      </div>

      {/* Size Variants */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Size Selector (Large Size)</h3>
        <OptionSelector
          options={sizeOptions}
          selectedOption={selectedSize}
          onSelect={setSelectedSize}
          size="large"
        />
        <p>Selected: {selectedSize}</p>
      </div>

      {/* Status Selector with Custom Class */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Status Selector (Small Size)</h3>
        <OptionSelector
          options={statusOptions}
          selectedOption={selectedStatus}
          onSelect={setSelectedStatus}
          size="small"
          className="custom-status-selector"
        />
        <p>Selected: {selectedStatus}</p>
      </div>

      {/* Disabled State */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Disabled Selector</h3>
        <OptionSelector
          options={unitOptions}
          selectedOption="bar"
          onSelect={() => {}}
          disabled={true}
        />
        <p>This selector is disabled</p>
      </div>
    </div>
  );
}
