import { ICON_TYPES } from '../constants';

// Import all icons
import thermoIcon from '../assets/thermo.png';
import flowIcon from '../assets/flow.png';
import feedIcon from '../assets/feed.png';
import calendarIcon from '../assets/calender.png';
import feedPressureIcon from '../assets/Group 7.png';
import dpIcon from '../assets/Group 8.png';
import qPermIcon from '../assets/Group 9.png';
import condPermIcon from '../assets/Group 10.png';

/**
 * Icon mapping for input types
 */
const INPUT_ICON_MAP = {
  [ICON_TYPES.TEMPERATURE]: thermoIcon,
  [ICON_TYPES.FLOW]: flowIcon,
  [ICON_TYPES.CONDUCTIVITY]: feedIcon,
  [ICON_TYPES.DEFAULT]: null
};

/**
 * Icon mapping for status cards
 */
export const STATUS_ICON_MAP = {
  feedPressure: feedPressureIcon,
  dP: dpIcon,
  qPerm: qPermIcon,
  condPerm: condPermIcon
};

/**
 * Get icon for input field based on type
 * @param {string} iconType - The icon type
 * @returns {string|null} Icon source or null
 */
export const getInputIcon = (iconType) => {
  return INPUT_ICON_MAP[iconType] || INPUT_ICON_MAP[ICON_TYPES.DEFAULT];
};

/**
 * Get calendar icon
 * @returns {string} Calendar icon source
 */
export const getCalendarIcon = () => {
  return calendarIcon;
};

/**
 * Get status card icon by parameter type
 * @param {string} parameterType - The parameter type
 * @returns {string} Icon source
 */
export const getStatusCardIcon = (parameterType) => {
  return STATUS_ICON_MAP[parameterType];
};

/**
 * Validate if an icon type is valid
 * @param {string} iconType - The icon type to validate
 * @returns {boolean} True if valid icon type
 */
export const isValidIconType = (iconType) => {
  return Object.values(ICON_TYPES).includes(iconType);
};
