import PropTypes from 'prop-types';
import { EditOutlined } from '@ant-design/icons';

export default function ButtonGroup({ options, selectedOption, onSelect, className = '', showCustomUnitsModal }) {
  // const { showCustomUnitsModal } = useCustomUnits();

  return (
    <div className={`button-group ${className}`.trim()}>
      {options.map((option) => (
        <button
          key={option.value}
          className={`button-group-item ${selectedOption === option.value ? 'selected' : ''}`}
          onClick={() => onSelect(option.value)}
          style={{ display: 'flex', alignItems: 'center' }}
          type="button"
        >
          {option.label}
          
            {option.value === 'Custom' && (
                            // <button
                            //   className={`edit-units-btn`}

                            //   title="Edit custom units"
                            // >
                      
                                <EditOutlined className="edit-icon" style={{ color: selectedOption === option.value ? '#ffffff' : '#128370' }}
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent triggering the button's onClick
                                    showCustomUnitsModal();
                                  }}
                                  title="Edit custom units"
                                />
                          )}

        </button>
      ))}
    </div>
  );
}

ButtonGroup.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired
    })
  ).isRequired,
  selectedOption: PropTypes.string.isRequired,
  onSelect: PropTypes.func.isRequired,
  className: PropTypes.string,
  showCustomUnitsModal: PropTypes.func
};
