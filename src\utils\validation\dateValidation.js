import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

// Constants for validation
export const DATE_FORMAT = 'MM/DD/YYYY';
export const MIN_GAP_DAYS = 31; // Minimum gap between dates in days

export const getDateValidationRules = ({
    startDate,
    currentDate,
    forecastDate,
    isStartDate,
    isCurrentDate,
    isForecastDate,
}) => {
    const rules = [
        {
            validator: async (_, value) => {
                if (!value) return Promise.resolve();

                const date = dayjs(value, DATE_FORMAT, true);
                if (!date.isValid()) {
                    return Promise.reject(new Error('Invalid date format. Use MM/DD/YYYY'));
                }

                // Check if the date is in the future for forecast date
                if (isForecastDate && date.isSameOrBefore(dayjs(), 'day')) {
                    return Promise.reject(new Error('Forecast date must be in the future'));
                }

                // Validate date relationships (Start < Current < Forecast)
                if (startDate && currentDate && forecastDate) {
                    const start = dayjs(startDate, DATE_FORMAT, true);
                    const current = dayjs(currentDate, DATE_FORMAT, true);
                    const forecast = dayjs(forecastDate, DATE_FORMAT, true);

                    if (start.isValid() && current.isValid() && forecast.isValid()) {
                        // Check the sequence
                        if (isStartDate && date.isSameOrAfter(current)) {
                            return Promise.reject(new Error('Start date must be before current date'));
                        }
                        if (isCurrentDate) {
                            if (date.isSameOrBefore(start)) {
                                return Promise.reject(new Error('Current date must be after start date'));
                            }
                            if (date.isSameOrAfter(forecast)) {
                                return Promise.reject(new Error('Current date must be before forecast date'));
                            }
                        }
                        if (isForecastDate && date.isSameOrBefore(current)) {
                            return Promise.reject(new Error('Forecast date must be after current date'));
                        }
                    }

                    // Check minimum gap between dates
                    if (isCurrentDate && start.isValid()) {
                        const gap = date.diff(start, 'day');
                        if (gap < MIN_GAP_DAYS) {
                            return Promise.reject(new Error(`Current date must be at least ${MIN_GAP_DAYS} days after start date`));
                        }
                    }
                    if (isForecastDate && current.isValid()) {
                        const gap = date.diff(current, 'day');
                        if (gap < MIN_GAP_DAYS) {
                            return Promise.reject(new Error(`Forecast date must be at least ${MIN_GAP_DAYS} days after current date`));
                        }
                    }
                }

                return Promise.resolve();
            }
        }
    ];

    return rules;
};

export const parseDate = (dateString) => {
    if (!dateString) return null;
    const date = dayjs(dateString, DATE_FORMAT, true);
    return date.isValid() ? date : null;
};
