import { <PERSON>, <PERSON><PERSON>, Card, message } from 'antd';
import NumericInput from './NumericInput';
import { FormProvider } from '../contexts/FormProvider';

/**
 * Simple test component to verify the updated NumericInput works correctly
 */
export default function NumericInputTest() {
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    try {
      console.log('Form values:', values);
      message.success('Form submitted successfully!');
    } catch (error) {
      message.error('Form submission failed');
    }
  };

  const handleValidate = async () => {
    try {
      const values = await form.validateFields();
      console.log('Validation passed:', values);
      message.success('All fields are valid!');
    } catch (errorInfo) {
      console.log('Validation failed:', errorInfo);
      message.error('Please fix the validation errors');
    }
  };

  return (
    <FormProvider>
      <div style={{ padding: '24px', maxWidth: '600px' }}>
        <h2>NumericInput Test</h2>
        
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Card title="Basic Validation Test" style={{ marginBottom: '24px' }}>
            <NumericInput
              name="temperature"
              label="Temperature"
              unit="°C"
              min={-50}
              max={100}
              required
              placeholder="Enter temperature (-50 to 100)"
            />
            
            <NumericInput
              name="pressure"
              label="Pressure"
              unit="bar"
              min={0}
              max={50}
              required
              placeholder="Enter pressure (0 to 50)"
            />
          </Card>

          <Card title="Dynamic Parameter Validation Test" style={{ marginBottom: '24px' }}>
            <NumericInput
              name={['parameters', 'feedPressure', 'start']}
              label="Feed Pressure (Start)"
              parameterType="feedPressure"
              fieldName="start"
              required
              placeholder="Enter start pressure"
            />
            
            <NumericInput
              name={['parameters', 'qPerm', 'start']}
              label="Flow Rate (Start)"
              parameterType="qPerm"
              fieldName="start"
              required
              placeholder="Enter start flow rate"
            />
          </Card>

          <div style={{ marginTop: '24px' }}>
            <Button type="primary" htmlType="submit" style={{ marginRight: '16px' }}>
              Submit Form
            </Button>
            <Button onClick={handleValidate}>
              Validate Fields
            </Button>
          </div>
        </Form>

        <Card title="Test Instructions" style={{ marginTop: '24px' }}>
          <h4>How to Test:</h4>
          <ol>
            <li>Try entering values outside the allowed ranges</li>
            <li>Leave required fields empty and click validate</li>
            <li>Enter valid values and submit the form</li>
            <li>Check the browser console for form values</li>
          </ol>
          
          <h4>Expected Behavior:</h4>
          <ul>
            <li>✅ Temperature: -50 to 100°C</li>
            <li>✅ Pressure: 0 to 50 bar</li>
            <li>✅ Feed Pressure: Dynamic validation based on parameter config</li>
            <li>✅ Flow Rate: Dynamic validation based on parameter config</li>
            <li>✅ Help text shows current constraints</li>
            <li>✅ Error messages include units</li>
          </ul>
        </Card>
      </div>
    </FormProvider>
  );
}
