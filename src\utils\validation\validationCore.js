/**
 * Core validation utilities and types
 * Centralized validation system for the RoAdvisorLite application
 */

/**
 * Standard validation result structure
 * @typedef {Object} ValidationResult
 * @property {boolean} isValid - Whether the value is valid
 * @property {string} error - Error message if invalid
 * @property {any} value - The validated/sanitized value
 * @property {Object} [metadata] - Optional metadata about the validation
 */

/**
 * Validation rule structure
 * @typedef {Object} ValidationRule
 * @property {string} type - Type of validation (required, number, range, etc.)
 * @property {any} constraint - Constraint value (min/max for range, pattern for regex, etc.)
 * @property {string} message - Custom error message
 */

/**
 * Field validation configuration
 * @typedef {Object} FieldValidation
 * @property {boolean} required - Whether field is required
 * @property {ValidationRule[]} rules - Array of validation rules
 * @property {Function} customValidator - Custom validation function
 */

/**
 * Validation error types
 */
export const VALIDATION_ERRORS = {
  REQUIRED: 'REQUIRED',
  INVALID_NUMBER: 'INVALID_NUMBER',
  OUT_OF_RANGE: 'OUT_OF_RANGE',
  INVALID_FORMAT: 'INVALID_FORMAT',
  CUSTOM: 'CUSTOM'
};

/**
 * Default error messages
 */
export const DEFAULT_ERROR_MESSAGES = {
  [VALIDATION_ERRORS.REQUIRED]: 'This field is required',
  [VALIDATION_ERRORS.INVALID_NUMBER]: 'Please enter a valid number',
  [VALIDATION_ERRORS.OUT_OF_RANGE]: 'Value is out of allowed range',
  [VALIDATION_ERRORS.INVALID_FORMAT]: 'Invalid format'
};

/**
 * Check if a value is empty/null/undefined
 * @param {any} value - Value to check
 * @returns {boolean} True if empty
 */
export const isEmpty = (value) => {
  return value === null || value === undefined || value === '' || 
         (typeof value === 'string' && value.trim() === '');
};

/**
 * Check if a value is a valid number
 * @param {any} value - Value to validate
 * @returns {boolean} True if valid number
 */
export const isValidNumber = (value) => {
  if (isEmpty(value)) return true; // Allow empty values
  const num = parseFloat(value);
  return !isNaN(num) && isFinite(num);
};

/**
 * Convert value to number safely
 * @param {any} value - Value to convert
 * @returns {number|null} Converted number or null if invalid
 */
export const toNumber = (value) => {
  if (isEmpty(value)) return null;
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
};

/**
 * Validate required field
 * @param {any} value - Value to validate
 * @param {string} customMessage - Custom error message
 * @returns {ValidationResult} Validation result
 */
export const validateRequired = (value, customMessage) => {
  const isValid = !isEmpty(value);
  return {
    isValid,
    error: isValid ? '' : (customMessage || DEFAULT_ERROR_MESSAGES[VALIDATION_ERRORS.REQUIRED]),
    value
  };
};

/**
 * Validate numeric value
 * @param {any} value - Value to validate
 * @param {string} customMessage - Custom error message
 * @returns {ValidationResult} Validation result
 */
export const validateNumber = (value, customMessage) => {
  if (isEmpty(value)) {
    return { isValid: true, error: '', value };
  }
  
  const isValid = isValidNumber(value);
  return {
    isValid,
    error: isValid ? '' : (customMessage || DEFAULT_ERROR_MESSAGES[VALIDATION_ERRORS.INVALID_NUMBER]),
    value: isValid ? toNumber(value) : value
  };
};

/**
 * Validate numeric range
 * @param {any} value - Value to validate
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @param {string} unit - Unit for error message
 * @param {string} customMessage - Custom error message
 * @returns {ValidationResult} Validation result
 */
export const validateRange = (value, min, max, unit = '', customMessage) => {
  if (isEmpty(value)) {
    return { isValid: true, error: '', value };
  }
  
  const numValue = toNumber(value);
  if (numValue === null) {
    return {
      isValid: false,
      error: DEFAULT_ERROR_MESSAGES[VALIDATION_ERRORS.INVALID_NUMBER],
      value
    };
  }
  
  let isValid = true;
  let error = '';
  
  if (min !== undefined && numValue < min) {
    isValid = false;
    error = customMessage || `Value must be at least ${min}${unit ? ' ' + unit : ''}`;
  } else if (max !== undefined && numValue > max) {
    isValid = false;
    error = customMessage || `Value must not exceed ${max}${unit ? ' ' + unit : ''}`;
  }
  
  return {
    isValid,
    error,
    value: numValue
  };
};

/**
 * Validate date format
 * @param {string} dateString - Date string to validate
 * @param {string} format - Expected format (default: MM/DD/YYYY)
 * @param {string} customMessage - Custom error message
 * @returns {ValidationResult} Validation result
 */
export const validateDate = (dateString, format = 'MM/DD/YYYY', customMessage) => {
  if (isEmpty(dateString)) {
    return { isValid: true, error: '', value: dateString };
  }
  
  // Basic date format validation
  const dateRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
  if (!dateRegex.test(dateString)) {
    return {
      isValid: false,
      error: customMessage || `Please enter date in ${format} format`,
      value: dateString
    };
  }
  
  // Try to parse the date
  const [month, day, year] = dateString.split('/').map(Number);
  const date = new Date(year, month - 1, day);
  
  const isValid = date.getFullYear() === year &&
                  date.getMonth() === month - 1 &&
                  date.getDate() === day;
  
  return {
    isValid,
    error: isValid ? '' : (customMessage || 'Please enter a valid date'),
    value: dateString
  };
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @param {string} customMessage - Custom error message
 * @returns {ValidationResult} Validation result
 */
export const validateEmail = (email, customMessage) => {
  if (isEmpty(email)) {
    return { isValid: true, error: '', value: email };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(email);
  
  return {
    isValid,
    error: isValid ? '' : (customMessage || 'Please enter a valid email address'),
    value: email
  };
};

/**
 * Run multiple validation rules on a value
 * @param {any} value - Value to validate
 * @param {ValidationRule[]} rules - Array of validation rules
 * @returns {ValidationResult} Combined validation result
 */
export const validateWithRules = (value, rules = []) => {
  let sanitizedValue = value;

  for (const rule of rules) {
    let result;
    
    switch (rule.type) {
      case 'required':
        result = validateRequired(value, rule.message);
        break;
      case 'number':
        result = validateNumber(value, rule.message);
        if (result.isValid) {
          sanitizedValue = result.value; // Keep the sanitized number
        }
        break;
      case 'range':
        result = validateRange(
          sanitizedValue, 
          rule.constraint?.min, 
          rule.constraint?.max,
          rule.constraint?.unit,
          rule.message
        );
        break;
      case 'date':
        result = validateDate(value, rule.constraint?.format, rule.message);
        break;
      case 'email':
        result = validateEmail(value, rule.message);
        break;
      case 'custom':
        if (typeof rule.constraint === 'function') {
          result = rule.constraint(value);
        }
        break;
      default:
        result = { isValid: true, error: '', value };
    }
    
    if (!result.isValid) {
      return { 
        isValid: false, 
        error: result.error, 
        value: sanitizedValue,
        metadata: result.metadata
      };
    }
  }
  
  return { 
    isValid: true, 
    error: '', 
    value: sanitizedValue,
    metadata: {}
  };
};

/**
 * Create a validation rule
 * @param {string} type - Rule type
 * @param {any} constraint - Rule constraint
 * @param {string} message - Custom error message
 * @returns {ValidationRule} Validation rule
 */
export const createValidationRule = (type, constraint, message) => ({
  type,
  constraint,
  message
});
