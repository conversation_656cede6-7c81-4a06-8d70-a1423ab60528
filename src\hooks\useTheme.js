import { theme } from 'antd';

/**
 * Custom hook to access Ant Design theme tokens
 * Provides easy access to theme colors, spacing, and other design tokens
 * 
 * @returns {Object} Theme tokens and utilities
 */
export function useTheme() {
  const { token } = theme.useToken();

  return {
    // Core theme tokens
    token,
    
    // Commonly used colors
    colors: {
      primary: token.colorPrimary,
      primaryHover: token.colorPrimaryHover,
      primaryActive: token.colorPrimaryActive,
      text: token.colorText,
      textSecondary: token.colorTextSecondary,
      textTertiary: token.colorTextTertiary,
      textDisabled: token.colorTextDisabled,
      background: token.colorBgContainer,
      backgroundElevated: token.colorBgElevated,
      backgroundLayout: token.colorBgLayout,
      border: token.colorBorder,
      borderSecondary: token.colorBorderSecondary,
      success: token.colorSuccess,
      warning: token.colorWarning,
      error: token.colorError,
      info: token.colorInfo,
    },
    
    // Typography
    typography: {
      fontFamily: token.fontFamily,
      fontSize: token.fontSize,
      fontSizeSM: token.fontSizeSM,
      fontSizeXS: token.fontSizeXS,
      fontSizeLG: token.fontSizeLG,
      fontWeightStrong: token.fontWeightStrong,
      lineHeight: token.lineHeight,
      lineHeightSM: token.lineHeightSM,
      lineHeightLG: token.lineHeightLG,
    },
    
    // Spacing
    spacing: {
      padding: token.padding,
      paddingSM: token.paddingSM,
      paddingXS: token.paddingXS,
      paddingLG: token.paddingLG,
      margin: token.margin,
      marginSM: token.marginSM,
      marginXS: token.marginXS,
      marginLG: token.marginLG,
    },
    
    // Layout
    layout: {
      borderRadius: token.borderRadius,
      borderRadiusLG: token.borderRadiusLG,
      borderRadiusSM: token.borderRadiusSM,
      controlHeight: token.controlHeight,
      controlHeightSM: token.controlHeightSM,
      controlHeightLG: token.controlHeightLG,
    },
    
    // Shadows
    shadows: {
      boxShadow: token.boxShadow,
      boxShadowSecondary: token.boxShadowSecondary,
    },
    
    // Utility functions
    utils: {
      /**
       * Get a color with opacity
       * @param {string} color - The color value
       * @param {number} opacity - Opacity value (0-1)
       * @returns {string} Color with opacity
       */
      withOpacity: (color, opacity) => {
        // Convert hex to rgba if needed
        if (color.startsWith('#')) {
          const hex = color.replace('#', '');
          const r = parseInt(hex.substr(0, 2), 16);
          const g = parseInt(hex.substr(2, 2), 16);
          const b = parseInt(hex.substr(4, 2), 16);
          return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }
        return color;
      },
      
      /**
       * Get responsive spacing based on screen size
       * @param {string} size - Size key (xs, sm, md, lg, xl)
       * @returns {number} Spacing value
       */
      getResponsiveSpacing: (size) => {
        const spacingMap = {
          xs: token.paddingXS,
          sm: token.paddingSM,
          md: token.padding,
          lg: token.paddingLG,
          xl: token.paddingLG * 1.5,
        };
        return spacingMap[size] || token.padding;
      },
    },
  };
}

/**
 * Hook to get theme-aware CSS variables
 * Useful for components that need to use theme values in CSS
 * 
 * @returns {Object} CSS custom properties object
 */
export function useThemeCSSVars() {
  const { token } = theme.useToken();
  
  return {
    '--ant-color-primary': token.colorPrimary,
    '--ant-color-primary-hover': token.colorPrimaryHover,
    '--ant-color-primary-active': token.colorPrimaryActive,
    '--ant-color-text': token.colorText,
    '--ant-color-text-secondary': token.colorTextSecondary,
    '--ant-color-bg-container': token.colorBgContainer,
    '--ant-color-border': token.colorBorder,
    '--ant-font-family': token.fontFamily,
    '--ant-font-size': `${token.fontSize}px`,
    '--ant-line-height': token.lineHeight,
    '--ant-border-radius': `${token.borderRadius}px`,
    '--ant-control-height': `${token.controlHeight}px`,
    '--ant-padding': `${token.padding}px`,
    '--ant-box-shadow': token.boxShadow,
  };
}

export default useTheme;
