/**
 * Form Actions Provider
 * Provides centralized form actions and state update functions
 */

import { useC<PERSON>back, useMemo, useContext } from 'react';
import { FormActionsContext, FormDataContext } from './FormContext.jsx';
import { convertValue, roundConvertedValue } from '../utils/unitConversion';

/**
 * Helper function to convert form data between unit systems
 * @param {Object} formData - The form data to convert
 * @param {string} fromSystem - Source unit system
 * @param {string} toSystem - Target unit system
 * @param {Object} fromCustomUnits - Custom units for source system
 * @param {Object} toCustomUnits - Custom units for target system
 * @returns {Object} Converted form data
 */
const convertFormData = (
  formData,
  fromSystem,
  toSystem,
  fromCustomUnits = {},
  toCustomUnits = {}
) => {
  // Check if conversion is needed
  if (fromSystem === toSystem) {
    // If both systems are 'Custom', check if the actual custom units are different
    if (fromSystem === 'Custom') {
      const customUnitsChanged =
        JSON.stringify(fromCustomUnits) !== JSON.stringify(toCustomUnits);
      if (!customUnitsChanged) {
        return formData; // No conversion needed if custom units are the same
      }
    } else {
      return formData; // No conversion needed for same non-custom systems
    }
  }

  const convertedData = {};

  Object.keys(formData).forEach(section => {
    if (typeof formData[section] === 'object' && formData[section] !== null) {
      convertedData[section] = {};

      Object.keys(formData[section]).forEach(field => {
        const value = formData[section][field];

        // Map form fields to parameter types for conversion
        let parameterType = null;
        if (field === 'temperature') {
          parameterType = 'temperature';
        } else if (field === 'feedFlow') {
          parameterType = 'flow';
        } else if (field === 'feedConductivity') {
          parameterType = 'conductivity';
        }

        if (parameterType && value && !isNaN(parseFloat(value))) {
          const originalValue = parseFloat(value);
          const convertedValue = convertValue(
            originalValue,
            parameterType,
            fromSystem,
            toSystem,
            fromCustomUnits,
            toCustomUnits
          );
          const roundedValue = roundConvertedValue(
            convertedValue,
            parameterType
          );
          console.log(
            `Converting ${field} (${parameterType}): ${originalValue} → ${convertedValue} → ${roundedValue}`
          );
          convertedData[section][field] = roundedValue.toString();
        } else {
          convertedData[section][field] = value;
        }
      });
    } else {
      convertedData[section] = formData[section];
    }
  });

  return convertedData;
};

/**
 * Helper function to convert parameters data between unit systems
 * @param {Object} parameters - The parameters to convert
 * @param {string} fromSystem - Source unit system
 * @param {string} toSystem - Target unit system
 * @param {Object} fromCustomUnits - Custom units for source system
 * @param {Object} toCustomUnits - Custom units for target system
 * @returns {Object} Converted parameters
 */
const convertParametersData = (
  parameters,
  fromSystem,
  toSystem,
  fromCustomUnits = {},
  toCustomUnits = {}
) => {
  // Check if conversion is needed
  if (fromSystem === toSystem) {
    // If both systems are 'Custom', check if the actual custom units are different
    if (fromSystem === 'Custom') {
      const customUnitsChanged =
        JSON.stringify(fromCustomUnits) !== JSON.stringify(toCustomUnits);
      if (!customUnitsChanged) {
        return parameters; // No conversion needed if custom units are the same
      }
    } else {
      return parameters; // No conversion needed for same non-custom systems
    }
  }

  const convertedParams = {};

  Object.keys(parameters).forEach(paramType => {
    if (
      typeof parameters[paramType] === 'object' &&
      parameters[paramType] !== null
    ) {
      convertedParams[paramType] = {};

      Object.keys(parameters[paramType]).forEach(field => {
        const value = parameters[paramType][field];

        // Map parameter types for conversion using exact parameter type mapping
        let conversionType = null;
        switch (paramType) {
          case 'feedPressure':
          case 'dP':
            conversionType = 'pressure';
            break;
          case 'qPerm':
            conversionType = 'flow';
            break;
          case 'condPerm':
            conversionType = 'conductivity';
            break;
          default:
            // Fallback to string matching for any other cases
            if (
              paramType.includes('pressure') ||
              paramType.includes('Pressure')
            ) {
              conversionType = 'pressure';
            } else if (
              paramType.includes('flow') ||
              paramType.includes('Flow') ||
              paramType.includes('perm')
            ) {
              conversionType = 'flow';
            } else if (
              paramType.includes('conductivity') ||
              paramType.includes('Conductivity') ||
              paramType.includes('cond')
            ) {
              conversionType = 'conductivity';
            }
            break;
        }

        if (
          conversionType &&
          value &&
          !isNaN(parseFloat(value)) &&
          field !== 'normalized' &&
          field !== 'threshold'
        ) {
          const convertedValue = convertValue(
            parseFloat(value),
            conversionType,
            fromSystem,
            toSystem,
            fromCustomUnits,
            toCustomUnits
          );
          convertedParams[paramType][field] = roundConvertedValue(
            convertedValue,
            conversionType
          ).toString();
        } else {
          convertedParams[paramType][field] = value;
        }
      });
    } else {
      convertedParams[paramType] = parameters[paramType];
    }
  });

  return convertedParams;
};

/**
 * Form Actions Provider Component
 * Provides all form actions through React Context with optimized memoization
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export function FormActionsProvider({ children }) {
  // Get form data context to access state setters
  const formDataContext = useContext(FormDataContext);

  if (!formDataContext) {
    throw new Error(
      'FormActionsProvider must be used within a FormDataProvider'
    );
  }

  const {
    formData,
    parameters,
    cipData,
    unitSystem,
    customUnits,
    setFormData,
    setParameters,
    setCipData,
    setUnitSystem,
  } = formDataContext;

  // Form data actions
  const updateFormField = useCallback(
    (section, field, value) => {
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value,
        },
      }));
    },
    [setFormData]
  );

  const updateFormSection = useCallback(
    (section, updates) => {
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          ...updates,
        },
      }));
    },
    [setFormData]
  );

  const resetFormData = useCallback(() => {
    // Reset to empty values - initial values are managed in FormDataProvider
    setFormData({
      start: { date: '', temperature: '', feedFlow: '', feedConductivity: '' },
      current: {
        date: '',
        temperature: '',
        feedFlow: '',
        feedConductivity: '',
      },
      forecast: {
        date: '',
        temperature: '',
        feedFlow: '',
        feedConductivity: '',
      },
    });
  }, [setFormData]);

  // Parameter actions
  const updateParameter = useCallback(
    (parameterType, field, value) => {
      setParameters(prev => ({
        ...prev,
        [parameterType]: {
          ...prev[parameterType],
          [field]: value,
        },
      }));
    },
    [setParameters]
  );

  const updateParameterFields = useCallback(
    (parameterType, updates) => {
      setParameters(prev => ({
        ...prev,
        [parameterType]: {
          ...prev[parameterType],
          ...updates,
        },
      }));
    },
    [setParameters]
  );

  const resetParameters = useCallback(() => {
    // Reset to empty values - initial values are managed in FormDataProvider
    setParameters({});
  }, [setParameters]);

  // CIP actions
  const updateCIPFrequency = useCallback(
    frequency => {
      setCipData(prev => ({
        ...prev,
        frequency: Math.max(0, Math.min(100, Number(frequency) || 0)),
      }));
    },
    [setCipData]
  );

  const updateCIPDuration = useCallback(
    duration => {
      setCipData(prev => ({
        ...prev,
        duration: duration,
      }));
    },
    [setCipData]
  );

  // Unit system actions
  const changeUnitSystem = useCallback(
    (newUnitSystem, conversionOptions = {}) => {
      const {
        formData: currentFormData,
        parameters: currentParameters,
        customUnits: newCustomUnits,
        onFormDataConverted,
        onParametersConverted,
      } = conversionOptions;

      // Get current unit system for conversion
      const currentUnitSystem = unitSystem;

      // Debug: Unit system change
      console.log('Unit system change:', {
        from: currentUnitSystem,
        to: newUnitSystem,
        hasFormData: !!currentFormData,
        hasParameters: !!currentParameters,
        hasConversionCallback: !!onFormDataConverted,
      });

      // Update unit system first
      setUnitSystem(newUnitSystem);

      // Determine custom units for conversion
      const fromCustomUnits = currentUnitSystem === 'Custom' ? customUnits : {};
      const toCustomUnits =
        newUnitSystem === 'Custom' ? newCustomUnits || customUnits : {};

      // Convert form data if provided
      if (
        currentFormData &&
        onFormDataConverted &&
        typeof onFormDataConverted === 'function'
      ) {
        console.log(
          'Converting form data from',
          currentUnitSystem,
          'to',
          newUnitSystem
        );
        const convertedFormData = convertFormData(
          currentFormData,
          currentUnitSystem,
          newUnitSystem,
          fromCustomUnits,
          toCustomUnits
        );
        console.log('Original form data:', currentFormData);
        console.log('Converted form data:', convertedFormData);
        onFormDataConverted(convertedFormData);
      }

      // Convert parameters if provided
      if (
        currentParameters &&
        onParametersConverted &&
        typeof onParametersConverted === 'function'
      ) {
        console.log(
          'Converting parameters from',
          currentUnitSystem,
          'to',
          newUnitSystem
        );
        const convertedParameters = convertParametersData(
          currentParameters,
          currentUnitSystem,
          newUnitSystem,
          fromCustomUnits,
          toCustomUnits
        );
        onParametersConverted(convertedParameters);
      }
    },
    [setUnitSystem, unitSystem, customUnits]
  );

  // Reset all form state
  const resetAllFormState = useCallback(() => {
    resetFormData();
    resetParameters();
    setCipData({ frequency: 15, duration: '00000' });
  }, [resetFormData, resetParameters, setCipData]);

  // Memoized context value
  const contextValue = useMemo(
    () => ({
      // Form data actions
      updateFormField,
      updateFormSection,
      resetFormData,

      // Parameter actions
      updateParameter,
      updateParameterFields,
      resetParameters,

      // CIP actions
      updateCIPFrequency,
      updateCIPDuration,

      // Unit system actions
      changeUnitSystem,

      // Global actions
      resetAllFormState,

      // Direct state setters (for unit conversion)
      setFormData,
      setParameters,
      setCipData,
      setUnitSystem,
    }),
    [
      updateFormField,
      updateFormSection,
      resetFormData,
      updateParameter,
      updateParameterFields,
      resetParameters,
      updateCIPFrequency,
      updateCIPDuration,
      changeUnitSystem,
      resetAllFormState,
      setFormData,
      setParameters,
      setCipData,
      setUnitSystem,
    ]
  );

  return (
    <FormActionsContext.Provider value={contextValue}>
      {children}
    </FormActionsContext.Provider>
  );
}

export default FormActionsProvider;
