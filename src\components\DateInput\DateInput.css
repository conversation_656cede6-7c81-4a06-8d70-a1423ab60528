/* Calendar container styling */
.ant-picker-dropdown {
  font-size: 12px !important;
}

.date-picker .ant-picker-input::placeholder{
  color: black !important;
  /* opacity: 0.7 !important; */
}

.date-picker .ant-picker-suffix {
  color: rgba(0, 0, 0, 0.8) !important;
  font-size: 14px !important;
 
  /* Change this color value to your desired color */ /* background-color: #dcd6d6 !important;
  height: 32px;
  width: 40px;
  flex:1; */
}


/* .date-picker{
  padding: 8px !important;
  padding-bottom: 15px !important;
} */

.date-picker:hover{

  border: none!important;
  box-shadow: 0 2px 4px rgba(194, 180, 255, 0.329);
}

.date-picker:active,
.date-picker:focus-visible,
.date-picker:focus-within {
    border-color: var(--brand-500) !important;

  box-shadow: 0 2px 4px rgba(194, 180, 255, 0.329);
}

.date-picker:focus{
    border-color: var(--brand-500) !important;

  box-shadow: 0 2px 4px rgba(194, 180, 255, 0.329);
}

/* Header styling */
.ant-picker-header {
  background-color: var(--gray-f8) !important;
  border-bottom: 1px solid var(--gray-e1) !important;
  padding: 8px !important;
}

/* Header buttons (prev/next month) */
.ant-picker-header-super-prev-btn,
.ant-picker-header-prev-btn,
.ant-picker-header-next-btn,
.ant-picker-header-super-next-btn {
  color: var(--gray-900) !important;
}

/* Month/Year text */
.ant-picker-header-view {
  color: var(--gray-900) !important;
  font-weight: 600 !important;
}

/* Calendar cells */
.ant-picker-cell {
  color: var(--gray-700) !important;
  padding: 2px 0 !important;
}

/* Today's date */
.ant-picker-cell-today .ant-picker-cell-inner {

  border-color: var(--gray-500) !important;
  color: var(--gray-900) !important;
}

/* Selected date */
.ant-picker-cell-selected .ant-picker-cell-inner {
  background-color: var(--gray-500) !important;
  /* color: var(--gray-900) !important; */
}

/* Hover state for dates */
.ant-picker-cell:hover .ant-picker-cell-inner {
  background-color: var(--gray-100) !important;
}

/* Footer (Today/Clear buttons) */
.ant-picker-footer {
  border-top: 1px solid var(--gray-e1) !important;
  padding: 4px !important;
}

/* Today button */
.ant-picker-today-btn {
  color: var(--gray-500) !important;
  font-size: 12px !important;
}