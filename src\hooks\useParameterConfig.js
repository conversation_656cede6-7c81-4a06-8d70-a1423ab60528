import { useMemo } from 'react';
import { useFormData } from '../contexts/useFormContext';
import { getParameterConfigWithLabels, getParameterConfig } from '../config/parameterConfig';
import { UNIT_SYSTEMS } from '../utils/unitConversion';

/**
 * Custom hook to get parameter configuration with labels appropriate for the current unit system
 * This hook automatically updates when the unit system changes
 * 
 * @returns {Object} Parameter configuration utilities
 */
export function useParameterConfig() {
  const { unitSystem } = useFormData() || { unitSystem: UNIT_SYSTEMS.METRIC };

  // Get parameter configuration with appropriate labels for current unit system
  const parameterConfig = useMemo(() => {
    return getParameterConfigWithLabels(unitSystem);
  }, [unitSystem]);

  // Function to get a specific parameter configuration by type
  const getParameterByType = useMemo(() => {
    return (parameterType) => {
      return getParameterConfig(parameterType, unitSystem);
    };
  }, [unitSystem]);

  // Function to get all parameter types
  const getAllTypes = useMemo(() => {
    return () => parameterConfig.map(config => config.type);
  }, [parameterConfig]);

  return {
    parameterConfig,
    getParameterByType,
    getAllTypes,
    unitSystem
  };
}

/**
 * Custom hook to get a specific parameter configuration by type
 * This hook automatically updates when the unit system changes
 * 
 * @param {string} parameterType - The parameter type to get configuration for
 * @returns {Object|null} Parameter configuration or null if not found
 */
export function useParameterConfigByType(parameterType) {
  const { unitSystem } = useFormData() || { unitSystem: UNIT_SYSTEMS.METRIC };

  const parameterConfig = useMemo(() => {
    return getParameterConfig(parameterType, unitSystem);
  }, [parameterType, unitSystem]);

  return parameterConfig;
}

export default useParameterConfig;
