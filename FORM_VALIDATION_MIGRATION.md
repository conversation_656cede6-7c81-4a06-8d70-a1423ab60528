# Form Validation Migration Guide

This guide shows how to migrate from the complex custom validation hooks to the simplified Ant Design Form.Item approach.

## Overview of Changes

### Before (Complex Custom Validation)
- Multiple custom hooks: `useParameterValidation`, `useNumericValidation`
- Manual error state management
- Complex validation logic spread across components
- Difficult to debug validation issues

### After (Simple Form.Item Validation)
- Single `Form.Item` with `rules` prop
- Automatic error handling by Ant Design
- Centralized validation rule generation
- Easy to debug and maintain

## Migration Steps

### 1. Replace NumericInput Component

#### Old Approach:
```jsx
// Old NumericInput with custom validation
import { useNumericValidation } from '../utils/validation/useValidation';

export default function NumericInput({ value, onChange, min, max, unit, required }) {
  const { error, validateAndUpdate } = useNumericValidation({ min, max, required, unit });
  
  const handleChange = (newValue) => {
    validateAndUpdate(newValue, onChange);
  };
  
  return (
    <div>
      <InputNumber
        value={value}
        onChange={handleChange}
        status={error ? 'error' : ''}
        addonAfter={unit}
      />
      {error && <div className="error-message">{error}</div>}
    </div>
  );
}
```

#### New Approach:
```jsx
// New FormNumericInput with Form.Item validation
import { Form, InputNumber } from 'antd';
import { createNumericValidationRules } from '../utils/validation/formRules';

export default function FormNumericInput({ name, label, min, max, unit, required }) {
  const rules = createNumericValidationRules({ min, max, unit, required });
  
  return (
    <Form.Item name={name} label={label} rules={rules}>
      <InputNumber addonAfter={unit} />
    </Form.Item>
  );
}
```

### 2. Replace ParameterRow Component

#### Old Approach:
```jsx
// Old ParameterRow with complex validation hooks
export default function ParameterRow({ parameterType, label }) {
  const { validateAndUpdate, shouldShowError, getError } = useParameterValidation(
    parameterType, unitSystem, customUnits
  );
  
  const ParameterInput = ({ value, fieldName, isReadOnly, isDisabled }) => {
    const error = shouldShowError(fieldName, isDisabled, isReadOnly) ? getError(fieldName) : '';
    
    const handleChange = (newValue) => {
      validateAndUpdate(newValue, (validatedValue) => {
        handleParameterChange(fieldName, validatedValue);
      }, fieldName, { isDisabled, isReadOnly });
    };
    
    return (
      <div>
        <InputNumber
          value={value}
          onChange={handleChange}
          status={error ? 'error' : ''}
          addonAfter={currentUnitLabel}
          readOnly={isReadOnly}
          disabled={isDisabled}
        />
        {error && <div className="error-message">{error}</div>}
      </div>
    );
  };
  
  return (
    <div className="parameter-table-row">
      <ParameterInput value={startValue} fieldName="start" />
      <ParameterInput value={currentValue} fieldName="current" />
      <ParameterInput value={normalizedValue} fieldName="normalized" isReadOnly />
      <ParameterInput value={limitValue} fieldName="limit" />
      <ParameterInput value={thresholdValue} fieldName="threshold" isReadOnly />
    </div>
  );
}
```

#### New Approach:
```jsx
// New FormParameterRow with Form.Item validation
export default function FormParameterRow({ parameterType, label }) {
  const rules = (fieldName) => createParameterValidationRules(
    parameterType, fieldName, unitSystem, customUnits
  );
  
  return (
    <div className="parameter-table-row">
      <Form.Item name={['parameters', parameterType, 'start']} rules={rules('start')}>
        <NumericInputField parameterType={parameterType} fieldName="start" />
      </Form.Item>
      
      <Form.Item name={['parameters', parameterType, 'current']} rules={rules('current')}>
        <NumericInputField parameterType={parameterType} fieldName="current" />
      </Form.Item>
      
      <Form.Item name={['parameters', parameterType, 'normalized']}>
        <NumericInputField parameterType={parameterType} fieldName="normalized" readOnly />
      </Form.Item>
      
      <Form.Item name={['parameters', parameterType, 'limit']} rules={rules('limit')}>
        <NumericInputField parameterType={parameterType} fieldName="limit" />
      </Form.Item>
      
      <Form.Item name={['parameters', parameterType, 'threshold']}>
        <NumericInputField parameterType={parameterType} fieldName="threshold" readOnly />
      </Form.Item>
    </div>
  );
}
```

## Key Benefits

### 1. Simplified Code
- **Before**: 150+ lines of validation logic per component
- **After**: 20-30 lines with Form.Item

### 2. Better Error Handling
- **Before**: Manual error state management, easy to get out of sync
- **After**: Automatic error handling by Ant Design

### 3. Easier Debugging
- **Before**: Complex hook interactions, hard to trace validation flow
- **After**: Clear validation rules, easy to debug

### 4. Dynamic Unit Conversion
- **Before**: Complex unit conversion logic mixed with validation
- **After**: Clean separation, validation rules generated dynamically

## Usage Examples

### Basic Numeric Input with Validation
```jsx
<FormNumericInput
  name="temperature"
  label="Temperature"
  unit="°C"
  min={-50}
  max={100}
  required
/>
```

### Parameter Field with Dynamic Validation
```jsx
<FormNumericInput
  name={['parameters', 'feedPressure', 'start']}
  label="Start Pressure"
  parameterType="feedPressure"
  fieldName="start"
  required
/>
```

### Complete Form with Parameter Table
```jsx
<Form onValuesChange={handleValuesChange}>
  <FormNumericInput name="temperature" label="Temperature" unit="°C" required />
  <FormParameterTable />
  <Button htmlType="submit">Submit</Button>
</Form>
```

## Dynamic Unit Conversion

The new system maintains all dynamic unit conversion functionality:

### Automatic Constraint Updates
```jsx
// When unit system changes from Metric to US:
// - Pressure limits: 0-100 bar → 0-1450 psi
// - Flow limits: 0-500 m³/h → 0-2201 gpm
// - Validation rules update automatically
// - Error messages show correct units
```

### Custom Unit Support
```jsx
// Custom units work seamlessly:
const rules = createParameterValidationRules(
  'feedPressure', 
  'start', 
  'Custom', 
  { pressure: 'psi' }  // Custom unit selection
);
// Returns: min: 0, max: 1450, unit: 'psi'
```

## Migration Checklist

- [ ] Replace `useParameterValidation` with `createParameterValidationRules`
- [ ] Replace `useNumericValidation` with `createNumericValidationRules`
- [ ] Wrap components with `Form` and use `Form.Item`
- [ ] Update error handling to use Form validation
- [ ] Test unit system switching
- [ ] Test custom unit validation
- [ ] Verify all validation constraints work
- [ ] Remove old validation hooks and files

## Files to Update

### New Files Created:
- `src/utils/validation/formRules.js` - Validation rule generators
- `src/components/FormNumericInput.jsx` - Form-based numeric input
- `src/components/ParameterTable/FormParameterRow.jsx` - Form-based parameter row
- `src/components/ParameterTable/FormParameterTable.jsx` - Form-based parameter table
- `src/components/FormBasedApp.jsx` - Complete form example

### Files to Deprecate:
- `src/utils/validation/useValidation.js` - Custom validation hooks
- `src/components/NumericInput.jsx` - Custom numeric input (keep for backward compatibility)
- `src/components/ParameterTable/ParameterRow.jsx` - Custom parameter row (keep for backward compatibility)

## Testing the New System

1. **Unit System Switching**: Change between Metric/US/Custom and verify validation limits update
2. **Real-time Validation**: Type invalid values and see immediate feedback
3. **Error Messages**: Verify error messages show correct units and limits
4. **Form Submission**: Test complete form validation on submit
5. **Custom Units**: Test custom unit selection and validation conversion

The new system is much simpler, more maintainable, and easier to debug while preserving all existing functionality!
