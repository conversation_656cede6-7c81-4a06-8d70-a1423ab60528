import { Row, Col, Typography, Form } from 'antd';
import { useEffect } from 'react';
import ParameterRow from './ParameterRow';
import { useParameterConfig } from '../../hooks/useParameterConfig';
import { useFormData, useFormActions } from '../../contexts';

const { Text } = Typography;

/**
 * Parameter Table Component
 * Displays parameter data in a table format using form context
 * No longer requires props - gets data from context
 */
export default function ParameterTable() {
  const { parameters } = useFormData();
  const { updateParameter } = useFormActions();
  const { parameterConfig } = useParameterConfig();

  // Form instance for handling converted parameter values
  const [parameterForm] = Form.useForm();

  // Update form fields when parameters change (e.g., after unit conversion)
  useEffect(() => {
    parameterForm.setFieldsValue({ parameters });
    parameterForm.validateFields();
  }, [parameterForm, parameters]);

  return (
    <div className="parameter-table-container">
      <Form
        form={parameterForm}
        layout="vertical"
        validateTrigger={['onChange', 'onBlur']}
        validateFirst
        initialValues={{ parameters }}
        onValuesChange={changedValues => {
          // Update parameter context when values change
          if (changedValues.parameters) {
            Object.keys(changedValues.parameters).forEach(parameterType => {
              if (changedValues.parameters[parameterType]) {
                Object.keys(changedValues.parameters[parameterType]).forEach(
                  field => {
                    updateParameter(
                      parameterType,
                      field,
                      changedValues.parameters[parameterType][field]
                    );
                  }
                );
              }
            });
          }
        }}
      >
        {/* Table Header */}
        <Row className="parameter-table-header" gutter={8}>
          <Col flex="100px">
            <div className="parameter-header-cell parameter-name-header"></div>
          </Col>
          <Col flex="1">
            <div className="parameter-header-cell">
              <Text className="parameter-header-text">Start</Text>
            </div>
          </Col>
          <Col flex="1">
            <div className="parameter-header-cell">
              <Text className="parameter-header-text">Current</Text>
            </div>
          </Col>
          <Col flex="1">
            <div className="parameter-header-cell">
              <Text className="parameter-header-text">Normalized</Text>
            </div>
          </Col>
          <Col flex="1">
            <div className="parameter-header-cell">
              <Text className="parameter-header-text">Limit</Text>
            </div>
          </Col>
          <Col flex="1">
            <div className="parameter-header-cell">
              <Text className="parameter-header-text">CIP Threshold</Text>
            </div>
          </Col>
        </Row>

        {/* Parameter Rows */}
        {parameterConfig.map(config => {
          return (
            <ParameterRow
              key={config.type}
              label={config.label}
              parameterType={config.type}
              thresholdDisabled={true} // Threshold fields should be disabled/read-only
            />
          );
        })}
      </Form>
    </div>
  );
}
