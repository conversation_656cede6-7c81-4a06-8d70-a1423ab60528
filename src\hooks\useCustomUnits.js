import { useState, useEffect } from 'react';
import { useCallback } from 'react';

const STORAGE_KEY = 'ro-advisor-custom-units';

/**
 * Hook to manage custom units with persistence
 * @returns {Object} Custom units state and handlers
 */
export function useCustomUnits() {
  // Load saved units from localStorage
  const loadSavedUnits = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('Error loading custom units:', error);
      return {};
    }
  };

  const [customUnits, setCustomUnits] = useState(loadSavedUnits);
  const [isCustomUnitsModalVisible, setIsCustomUnitsModalVisible] = useState(false);

  // Save units to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(customUnits));
    } catch (error) {
      console.error('Error saving custom units:', error);
    }
  }, [customUnits]);

  const handleCustomUnitsSave = (units) => {
    setCustomUnits(units);
    setIsCustomUnitsModalVisible(false);
  };

  const handleCustomUnitsCancel = () => {
    setIsCustomUnitsModalVisible(false);
  };

  const showCustomUnitsModal = useCallback(() => {
    console.log('Showing custom units modal');
    setIsCustomUnitsModalVisible(true);
  }, []);

  return {
    customUnits,
    isCustomUnitsModalVisible,
    showCustomUnitsModal,
    handleCustomUnitsSave,
    handleCustomUnitsCancel
  };
}
