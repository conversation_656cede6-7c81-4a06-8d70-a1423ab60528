import {
  calculateNCondPerm,
  calculateNFP,
  calculateNFR,
  calculateNQPerm,
} from "../../calculation";
import { PARAMETER_TYPES } from "../../constants";

const safeParseFloat = (value) => parseFloat(value) || 0;

const isValidResult = (result) => isFinite(result) && !isNaN(result);

const extractFormParameters = (formData) => ({
  initialTemperature: safeParseFloat(formData.start?.temperature),
  currentTemperature: safeParseFloat(formData.current?.temperature),
  flowRate: safeParseFloat(formData.current?.feedFlow),
  feedConductivity: safeParseFloat(formData.current?.feedConductivity),
});

const extractParameterValues = (allParameters) => ({
  pressureDrop: safeParseFloat(allParameters[PARAMETER_TYPES.DP]?.current),
  initialQPerm: safeParseFloat(allParameters[PARAMETER_TYPES.Q_PERM]?.start),
  currentQPerm: safeParseFloat(allParameters[PARAMETER_TYPES.Q_PERM]?.current),
});

const calculateNormalizedValue = (parameterType, startValue, currentValue, formData, allParameters, fallbackValue) => {
  if (!startValue || !currentValue || !formData) return fallbackValue || '';

  const formParams = extractFormParameters(formData);
  const paramValues = extractParameterValues(allParameters);

  try {
    const calculations = {
      [PARAMETER_TYPES.FEED_PRESSURE]: () => {
        const nfp = calculateNFP(
          safeParseFloat(currentValue),
          formParams.flowRate,
          paramValues.pressureDrop,
          formParams.feedConductivity,
          safeParseFloat(startValue),
          formParams.currentTemperature
        );
        return isValidResult(nfp) ? nfp.toFixed(2) : null;
      },
      [PARAMETER_TYPES.DP]: () => {
        const nfr = calculateNFR(
          formParams.flowRate,
          paramValues.initialQPerm,
          paramValues.currentQPerm,
          formParams.initialTemperature,
          formParams.currentTemperature
        );
        return isValidResult(nfr) ? nfr.toFixed(2) : null;
      },
      [PARAMETER_TYPES.Q_PERM]: () => {
        const nQPerm = calculateNQPerm(
          safeParseFloat(currentValue),
          formParams.initialTemperature,
          formParams.currentTemperature
        );
        return isValidResult(nQPerm) ? nQPerm.toFixed(2) : null;
      },
      [PARAMETER_TYPES.COND_PERM]: () => {
        const nCPerm = calculateNCondPerm(
          safeParseFloat(currentValue),
          formParams.initialTemperature,
          formParams.currentTemperature
        );
        return isValidResult(nCPerm) ? nCPerm.toFixed(2) : null;
      }
    };

    return calculations[parameterType]?.() || fallbackValue || '';
  } catch (error) {
    console.warn('Error calculating normalized value:', error);
    return fallbackValue || '';
  }
};

export { calculateNormalizedValue };
