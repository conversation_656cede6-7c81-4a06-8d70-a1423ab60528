/**
 * Validation message utilities and templates
 * @module validation/messages
 */

/**
 * Get a validation message template
 * @param {string} key - Template key
 * @param {Object} params - Template parameters
 * @returns {string} Formatted message
 */
export const getValidationMessage = (key, params = {}) => {
  const templates = {
    required: 'This field is required',
    invalidNumber: 'Please enter a valid number',
    range: `Value must be between ${params.min}${params.unit ? ' ' + params.unit : ''} and ${params.max}${params.unit ? ' ' + params.unit : ''}`,
    min: `Value must be at least ${params.min}${params.unit ? ' ' + params.unit : ''}`,
    max: `Value cannot exceed ${params.max}${params.unit ? ' ' + params.unit : ''}`,
    invalidDate: `Please enter a valid date in ${params.format || 'MM/DD/YYYY'} format`,
    invalidEmail: 'Please enter a valid email address',
    custom: params.message || 'Invalid value'
  };

  return templates[key] || templates.custom;
};

/**
 * Create a parameter-specific error message
 * @param {Object} params - Error parameters
 * @returns {string} Formatted error message
 */
export const createParameterErrorMessage = ({
  fieldName,
  parameterType,
  errorType,
  constraints = {},
  unitSystem
}) => {
  const baseMessage = getValidationMessage(errorType, constraints);
  return `${baseMessage} for ${fieldName} ${parameterType} in ${unitSystem} units`;
};
