import PropTypes from 'prop-types';
import { getStatusConfig } from '../utils/statusUtils';
import { STATUS_TYPES } from '../constants';
import { getCalendarIcon } from '../utils/iconUtils';
// antdIcons
import { CalendarOutlined } from '@ant-design/icons';

export default function StatusCard({
  title,
  value,
  status,
  estimatedLife,
  date,
  months,
  iconSrc,
  isLoading = false
}) {
  const statusConfig = getStatusConfig(status);

  if (isLoading) {
    return (
      <div className="status-card status-card-loading">
        <div className="status-card-content">
          <div className="status-card-left">
            <div className="status-icon-container loading-icon">
              <div className="loading-spinner"></div>
            </div>
            <div className="status-info">
              <h4 className="status-title">{title}</h4>
              {/* <div className="loading-text">Calculating...</div> */}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="status-card">
      <div className="status-card-content">
        <div className="status-card-left">
          <div className="status-icon-container">
            <img src={iconSrc} alt={title} className="status-icon-img" />
          </div>
          <div className="status-info">
            <h4 className="status-title">{title}</h4>
            <div className="status-value">{value}</div>
            <div className="status-indicator">
              <span
                className="status-icon-small"
                style={{ color: statusConfig.color }}
              >
                {statusConfig.icon}
              </span>
              <span
                className="status-text"
                style={{ color: statusConfig.color }}
              >
                {statusConfig.text}
              </span>
            </div>
          </div>
        </div>

        {estimatedLife && (
          <div className="status-card-right">
            <div className="estimated-life-section">
              <span className="estimated-life-label">Estimated Life</span>
              <div className="life-details">
                <div className="calendar-icon"><CalendarOutlined></CalendarOutlined></div>
                <span className="life-date">{date}</span>
                <span className="life-duration">{months} months</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

StatusCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.string,
  status: PropTypes.oneOf(Object.values(STATUS_TYPES)),
  estimatedLife: PropTypes.bool,
  date: PropTypes.string,
  months: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  iconSrc: PropTypes.string,
  isLoading: PropTypes.bool
};
