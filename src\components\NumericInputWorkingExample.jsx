import { <PERSON>, <PERSON><PERSON>, Card, message, Space } from 'antd';
import { useState } from 'react';
import NumericInput from './NumericInput';
import { FormProvider } from '../contexts/FormProvider';

/**
 * Working example that demonstrates NumericInput validation
 * This shows the correct way to use the updated NumericInput component
 */
export default function NumericInputWorkingExample() {
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState({});

  const handleSubmit = async (values) => {
    try {
      console.log('Form submitted with values:', values);
      message.success('Form submitted successfully!');
      setFormValues(values);
    } catch (error) {
      message.error('Form submission failed');
    }
  };

  const handleValidate = async () => {
    try {
      const values = await form.validateFields();
      console.log('Validation passed:', values);
      message.success('All fields are valid!');
    } catch (errorInfo) {
      console.log('Validation failed:', errorInfo);
      message.error(`Validation failed: ${errorInfo.errorFields?.length || 0} errors found`);
    }
  };

  const handleValuesChange = (changedValues, allValues) => {
    console.log('Form values changed:', changedValues, allValues);
    setFormValues(allValues);
  };

  const triggerValidation = () => {
    // Manually trigger validation for all fields
    form.validateFields()
      .then(() => {
        message.success('All fields are valid!');
      })
      .catch((errorInfo) => {
        message.error(`Found ${errorInfo.errorFields?.length || 0} validation errors`);
      });
  };

  return (
    <FormProvider>
      <div style={{ padding: '24px', maxWidth: '800px' }}>
        <h2>NumericInput Validation Demo</h2>
        <p>This example shows working validation with the updated NumericInput component.</p>
        
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={handleValuesChange}
          validateTrigger={['onChange', 'onBlur']}
        >
          <Card title="Test Validation" style={{ marginBottom: '24px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <NumericInput
                name="temperature"
                label="Temperature (Required, -50 to 100°C)"
                unit="°C"
                min={-50}
                max={100}
                required
                placeholder="Enter temperature between -50 and 100"
              />
              
              <NumericInput
                name="pressure"
                label="Pressure (Required, 0 to 50 bar)"
                unit="bar"
                min={0}
                max={50}
                required
                placeholder="Enter pressure between 0 and 50"
              />
              
              <NumericInput
                name="flow"
                label="Flow Rate (Optional, 0 to 1000 m³/h)"
                unit="m³/h"
                min={0}
                max={1000}
                placeholder="Enter flow rate between 0 and 1000"
              />
            </Space>
          </Card>

          <Card title="Dynamic Parameter Validation" style={{ marginBottom: '24px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <NumericInput
                name={['parameters', 'feedPressure', 'start']}
                label="Feed Pressure Start (Dynamic validation based on unit system)"
                parameterType="feedPressure"
                fieldName="start"
                required
                placeholder="Enter feed pressure start value"
              />
              
              <NumericInput
                name={['parameters', 'qPerm', 'current']}
                label="Flow Rate Current (Dynamic validation)"
                parameterType="qPerm"
                fieldName="current"
                required
                placeholder="Enter current flow rate"
              />
            </Space>
          </Card>

          <Card title="Actions" style={{ marginBottom: '24px' }}>
            <Space>
              <Button type="primary" htmlType="submit">
                Submit Form
              </Button>
              <Button onClick={handleValidate}>
                Validate All Fields
              </Button>
              <Button onClick={triggerValidation}>
                Trigger Validation
              </Button>
              <Button onClick={() => form.resetFields()}>
                Reset Form
              </Button>
            </Space>
          </Card>
        </Form>

        <Card title="Current Form Values" style={{ marginBottom: '24px' }}>
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(formValues, null, 2)}
          </pre>
        </Card>

        <Card title="How to Test Validation">
          <h4>Try these actions to see validation in action:</h4>
          <ol>
            <li><strong>Enter invalid values:</strong>
              <ul>
                <li>Temperature: Try entering 150 (should show "Value must not exceed 100°C")</li>
                <li>Temperature: Try entering -100 (should show "Value must be at least -50°C")</li>
                <li>Pressure: Try entering 100 (should show "Value must not exceed 50 bar")</li>
              </ul>
            </li>
            <li><strong>Leave required fields empty:</strong>
              <ul>
                <li>Clear the temperature field and click "Validate All Fields"</li>
                <li>Should show "This field is required"</li>
              </ul>
            </li>
            <li><strong>Enter non-numeric values:</strong>
              <ul>
                <li>Try typing "abc" in any field</li>
                <li>Should show "Please enter a valid number"</li>
              </ul>
            </li>
            <li><strong>Valid values:</strong>
              <ul>
                <li>Temperature: 25</li>
                <li>Pressure: 10</li>
                <li>Flow: 500</li>
              </ul>
            </li>
          </ol>
          
          <h4>Expected Behavior:</h4>
          <ul>
            <li>✅ Real-time validation as you type</li>
            <li>✅ Error messages appear below fields</li>
            <li>✅ Help text shows valid ranges</li>
            <li>✅ Submit button validates entire form</li>
            <li>✅ Console shows form values and validation results</li>
          </ul>
        </Card>
      </div>
    </FormProvider>
  );
}
