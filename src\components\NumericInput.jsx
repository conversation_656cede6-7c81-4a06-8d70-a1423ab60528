import { useMemo, useState } from 'react';
import { Form, InputNumber } from 'antd';
import PropTypes from 'prop-types';
import { useFormData } from '../contexts/useFormContext';
import { getParameterFieldValidation } from '../config/parameterConfig';
import { getFormFieldValidation } from '../config/formFieldConfig';

export default function NumericInput({
  name,
  label,
  onChange,
  unit,
  addonAfter,
  disabled = false,
  placeholder = '',
  min,
  max,
  step = 'any',
  required = false,
  parameterType, // New prop for dynamic validation
  fieldName, // New prop for dynamic validation
  formFieldType, // New prop for form field validation (temperature, feedFlow, feedConductivity)
  isReadOnly = false,
  className = '',
  style = {},
  ...formItemProps
}) {
  const { unitSystem, customUnits, parameters } = useFormData() || {
    unitSystem: 'US',
    customUnits: {},
    parameters: {},
  };

  // Dynamically determine constraints based on unit system
  const constraints = useMemo(() => {
    // Check for parameter field validation first (parameter table fields)
    if (parameterType && fieldName) {
      const fieldValidation = getParameterFieldValidation(
        parameterType,
        fieldName,
        unitSystem,
        customUnits
      );

      if (fieldValidation) {
        return {
          min: fieldValidation.min,
          max: fieldValidation.max,
          unit: fieldValidation.unit || unit,
          required,
        };
      }
    }

    // Check for form field validation (main form fields)
    if (formFieldType) {
      const fieldValidation = getFormFieldValidation(
        formFieldType,
        unitSystem,
        customUnits
      );

      if (fieldValidation) {
        return {
          min: fieldValidation.min,
          max: fieldValidation.max,
          unit: fieldValidation.unit || unit,
          required,
        };
      }
    }

    // Fall back to props if no dynamic constraints
    return { min, max, unit, required };
  }, [
    parameterType,
    fieldName,
    formFieldType,
    unitSystem,
    customUnits,
    min,
    max,
    unit,
    required,
  ]);

  // State for tracking validation errors to control help text display
  const [validationError, setValidationError] = useState(null);
  const [hasError, setHasError] = useState(false);

  // Generate constraint help text
  const constraintHelpText = useMemo(() => {
    const {
      min: constraintMin,
      max: constraintMax,
      unit: constraintUnit,
    } = constraints;

    if (constraintMin !== undefined && constraintMax !== undefined) {
      return `Range: ${constraintMin} - ${constraintMax}${constraintUnit ? ' ' + constraintUnit : ''}`;
    } else if (constraintMin !== undefined) {
      return `Minimum: ${constraintMin}${constraintUnit ? ' ' + constraintUnit : ''}`;
    } else if (constraintMax !== undefined) {
      return `Maximum: ${constraintMax}${constraintUnit ? ' ' + constraintUnit : ''}`;
    }
    return '';
  }, [constraints]);

  // Dynamic help text that switches between constraints and errors
  const helpText = useMemo(() => {
    // Show error message if validation failed
    if (hasError && validationError) {
      return validationError;
    }
    // Otherwise show constraint ranges for guidance
    return constraintHelpText;
  }, [hasError, validationError, constraintHelpText]);

  // Generate validation rules using dynamic constraints
  const validationRules = useMemo(() => {
    const rules = [];
    const {
      min: constraintMin,
      max: constraintMax,
      required: constraintRequired,
      unit: constraintUnit,
    } = constraints;

    if (constraintRequired) {
      rules.push({
        required: true,
        validator: (_, value) => {
          // Handle InputNumber's empty states properly
          if (
            value === null ||
            value === undefined ||
            value === '' ||
            (typeof value === 'string' && value.trim() === '')
          ) {
            const errorMsg = 'This field is required';
            setValidationError(errorMsg);
            setHasError(true);
            return Promise.reject(new Error(errorMsg));
          }
          // Clear error if value is provided
          setHasError(false);
          setValidationError(null);
          return Promise.resolve();
        },
      });
    }

    // Number validation with transform
    rules.push({
      validator: (_, value) => {
        // Skip validation for empty values (handled by required rule above)
        if (value === '' || value === null || value === undefined) {
          return Promise.resolve();
        }

        // Convert to string to handle InputNumber behavior
        const stringValue = String(value);
        const numValue = Number(stringValue);

        if (isNaN(numValue) || stringValue.trim() === '') {
          return Promise.reject(new Error('Please enter a valid number'));
        }

        return Promise.resolve();
      },
    });

    // Min value validation
    if (typeof constraintMin === 'number') {
      rules.push({
        validator: (_, value) => {
          // Skip validation for empty values (handled by required rule above)
          if (value === '' || value === null || value === undefined) {
            setHasError(false);
            setValidationError(null);
            return Promise.resolve();
          }

          const numValue = Number(value);
          if (!isNaN(numValue) && numValue < constraintMin) {
            const errorMsg = `Value must be at least ${constraintMin}${constraintUnit ? ' ' + constraintUnit : ''}`;
            setValidationError(errorMsg);
            setHasError(true);
            return Promise.reject(new Error(errorMsg));
          }

          setHasError(false);
          setValidationError(null);
          return Promise.resolve();
        },
      });
    }

    // Max value validation
    if (typeof constraintMax === 'number') {
      rules.push({
        validator: (_, value) => {
          if (value === '' || value === null || value === undefined) {
            setHasError(false);
            setValidationError(null);
            return Promise.resolve();
          }

          const numValue = Number(value);
          if (!isNaN(numValue) && numValue > constraintMax) {
            const errorMsg = `Value must not exceed ${constraintMax}${constraintUnit ? ' ' + constraintUnit : ''}`;
            setValidationError(errorMsg);
            setHasError(true);
            return Promise.reject(new Error(errorMsg));
          }

          setHasError(false);
          setValidationError(null);
          return Promise.resolve();
        },
      });
    }

    // Cross-field validation: Feed Conductivity should be greater than Conductivity Permeate
    if (
      formFieldType === 'feedConductivity' ||
      (Array.isArray(name) && name[1] === 'feedConductivity')
    ) {
      rules.push({
        validator: (_, value) => {
          // Skip validation for empty values
          if (value === '' || value === null || value === undefined) {
            setHasError(false);
            setValidationError(null);
            return Promise.resolve();
          }

          const numValue = Number(value);
          if (isNaN(numValue)) {
            return Promise.resolve(); // Let other validators handle non-numeric values
          }

          // Get the section and corresponding conductivity permeate value
          const section = Array.isArray(name) ? name[0] : 'current';

          // Get conductivity permeate value based on section
          let condPermValue;
          if (section === 'start') {
            condPermValue = parameters?.condPerm?.start;
          } else if (section === 'current') {
            condPermValue = parameters?.condPerm?.current;
          } else {
            // For forecast or other sections, use current as reference
            condPermValue = parameters?.condPerm?.current;
          }

          if (condPermValue !== undefined && condPermValue !== null) {
            const condPermNumValue = Number(condPermValue);

            if (!isNaN(condPermNumValue) && numValue <= condPermNumValue) {
              const errorMsg =
                'Feed conductivity must be greater than conductivity permeate';
              setValidationError(errorMsg);
              setHasError(true);
              return Promise.reject(new Error(errorMsg));
            }
          }

          // Clear error if validation passes
          setHasError(false);
          setValidationError(null);
          return Promise.resolve();
        },
      });
    }

    return rules;
  }, [
    constraints,
    formFieldType,
    name,
    parameters,
    setValidationError,
    setHasError,
  ]);

  return (
    <div style={{ position: 'relative', marginBottom: '16px' }}>
      <Form.Item
        name={name}
        label={label}
        rules={validationRules}
        className={className}
        style={{ marginBottom: '0px', ...style }}
        validateTrigger={['onChange', 'onBlur']}
        validateFirst={true}
        help={helpText}
        {...formItemProps}
      >
        <InputNumber
          onChange={onChange}
          disabled={disabled || isReadOnly}
          placeholder={placeholder}
          step={step}
          className="unified-input-addon"
          controls={false}
          size="small"
          addonAfter={addonAfter || constraints.unit || unit}
          style={{ width: '100%' }}
          type="number"
        />
      </Form.Item>
    </div>
  );
}

NumericInput.propTypes = {
  name: PropTypes.oneOfType([PropTypes.string, PropTypes.array]).isRequired,
  label: PropTypes.string,

  onChange: PropTypes.func,
  unit: PropTypes.string,
  addonAfter: PropTypes.node,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  min: PropTypes.number,
  max: PropTypes.number,
  step: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  required: PropTypes.bool,
  parameterType: PropTypes.string, // New prop for dynamic validation
  fieldName: PropTypes.string, // New prop for dynamic validation
  formFieldType: PropTypes.string, // New prop for form field validation
  isReadOnly: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
};
