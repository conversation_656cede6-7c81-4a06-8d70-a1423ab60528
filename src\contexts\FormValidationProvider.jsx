/**
 * Form Validation Provider
 * Simplified validation integration with form context
 */

import { useState, useCallback, useMemo } from 'react';
import { FormValidationContext } from './FormContext.jsx';

/**
 * Form Validation Provider Component
 * Simplified validation state management
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export function FormValidationProvider({ children }) {
  // Simplified validation state
  const [validationErrors, setValidationErrors] = useState({});

  // Basic validation functions
  const setFieldError = useCallback((fieldPath, error) => {
    setValidationErrors(prev => ({
      ...prev,
      [fieldPath]: error
    }));
  }, []);

  const clearFieldError = useCallback((fieldPath) => {
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldPath];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setValidationErrors({});
  }, []);

  const getFieldError = useCallback((fieldPath) => {
    return validationErrors[fieldPath] || '';
  }, [validationErrors]);

  const hasFieldError = useCallback((fieldPath) => {
    return Boolean(validationErrors[fieldPath]);
  }, [validationErrors]);

  // Simple validation - just basic error management
  const validateField = useCallback((fieldPath, value) => {
    // Basic validation - can be extended later
    const result = { isValid: true, error: '', value };

    // Update errors if needed
    if (result.isValid) {
      clearFieldError(fieldPath);
    } else {
      setFieldError(fieldPath, result.error);
    }

    return result;
  }, [clearFieldError, setFieldError]);

  // Simple form validation status
  const isFormValid = useMemo(() => {
    return Object.keys(validationErrors).length === 0;
  }, [validationErrors]);

  // Simplified context value
  const contextValue = useMemo(() => ({
    validationErrors,
    validateField,
    clearFieldError,
    clearAllErrors,
    getFieldError,
    hasFieldError,
    isFormValid,
    setFieldError
  }), [
    validationErrors,
    validateField,
    clearFieldError,
    clearAllErrors,
    getFieldError,
    hasFieldError,
    isFormValid,
    setFieldError
  ]);

  return (
    <FormValidationContext.Provider value={contextValue}>
      {children}
    </FormValidationContext.Provider>
  );
}

export default FormValidationProvider;
