export const getDateThreeYearsAgo = () => {
  const today = new Date();
  const threeYearsAgo = new Date(today.getFullYear() - 3, today.getMonth(), today.getDate());

  const month = (threeYearsAgo.getMonth() + 1).toString().padStart(2, '0');
  const day = threeYearsAgo.getDate().toString().padStart(2, '0');
  const year = threeYearsAgo.getFullYear();

  return `${month}/${day}/${year}`;
};


//current date
export const getCurrentDate = () => {
  const today = new Date();
  const month = (today.getMonth() + 1).toString().padStart(2, '0');
  const day = today.getDate().toString().padStart(2, '0');
  const year = today.getFullYear();

  return `${month}/${day}/${year}`;
};


//current date + 3 year
export const getDateThreeYearsFromNow = () => {
    const today = new Date();
    const threeYearsFromNow = new Date(today.getFullYear() + 3, today.getMonth(), today.getDate());
    
    const month = (threeYearsFromNow.getMonth() + 1).toString().padStart(2, '0');
    const day = threeYearsFromNow.getDate().toString().padStart(2, '0');
    const year = threeYearsFromNow.getFullYear();
    
    return `${month}/${day}/${year}`;
    };