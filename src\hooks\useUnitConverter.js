import { useState, useCallback, useMemo } from 'react';
import {
  UNIT_SYSTEMS,
  convertValue,
  getUnitLabel,
  convertValidationLimits,
  roundConvertedValue
} from '../utils/unitConversion';

/**
 * Custom hook for managing unit conversion throughout the application
 * Provides unit system state, conversion functions, and unit labels
 */
export const useUnitConverter = (initialUnitSystem = UNIT_SYSTEMS.METRIC) => {
  const [unitSystem, setUnitSystem] = useState(initialUnitSystem);

  /**
   * Convert a single value between unit systems
   * @param {number|string} value - The value to convert
   * @param {string} parameterType - Type of parameter ('pressure', 'flow', 'conductivity', 'temperature')
   * @param {string} fromSystem - Source unit system (optional, defaults to current system)
   * @param {string} toSystem - Target unit system (optional, defaults to current system)
   * @returns {number} Converted and rounded value
   */
  const convert = useCallback((value, parameterType, fromSystem = unitSystem, toSystem = unitSystem) => {
    const convertedValue = convertValue(value, parameterType, fromSystem, toSystem);
    return roundConvertedValue(convertedValue, parameterType);
  }, [unitSystem]);

  /**
   * Convert form data values when switching unit systems
   * @param {Object} formData - The form data object to convert
   * @param {string} fromSystem - Source unit system
   * @param {string} toSystem - Target unit system
   * @returns {Object} Converted form data
   */
  const convertFormData = useCallback((formData, fromSystem, toSystem) => {
    if (!formData || fromSystem === toSystem) return formData;

    const convertedData = {};

    Object.keys(formData).forEach(section => {
      if (typeof formData[section] === 'object' && formData[section] !== null) {
        convertedData[section] = {};
        
        Object.keys(formData[section]).forEach(field => {
          const value = formData[section][field];
          
          // Map form fields to parameter types for conversion
          let parameterType = null;
          if (field === 'temperature') {
            parameterType = 'temperature';
          } else if (field === 'feedFlow') {
            parameterType = 'flow';
          } else if (field === 'feedConductivity') {
            parameterType = 'conductivity';
          }
          
          if (parameterType && value) {
            convertedData[section][field] = convert(value, parameterType, fromSystem, toSystem).toString();
          } else {
            convertedData[section][field] = value;
          }
        });
      } else {
        convertedData[section] = formData[section];
      }
    });

    return convertedData;
  }, [convert]);

  /**
   * Convert parameter data when switching unit systems
   * @param {Object} parameters - The parameters object to convert
   * @param {string} fromSystem - Source unit system
   * @param {string} toSystem - Target unit system
   * @returns {Object} Converted parameters
   */
  const convertParameters = useCallback((parameters, fromSystem, toSystem) => {
    if (!parameters || fromSystem === toSystem) return parameters;

    const convertedParameters = {};

    Object.keys(parameters).forEach(parameterType => {
      if (typeof parameters[parameterType] === 'object' && parameters[parameterType] !== null) {
        convertedParameters[parameterType] = {};
        
        // Map parameter types to conversion types
        let conversionType = null;
        if (parameterType.includes('Pressure') || parameterType === 'feedPressure' || parameterType === 'dP') {
          conversionType = 'pressure';
        } else if (parameterType.includes('Perm') || parameterType === 'qPerm') {
          conversionType = 'flow';
        } else if (parameterType.includes('Cond') || parameterType === 'condPerm') {
          conversionType = 'conductivity';
        }
        
        Object.keys(parameters[parameterType]).forEach(field => {
          const value = parameters[parameterType][field];
          
          // Skip conversion for normalized and threshold fields as they are calculated
          if (field === 'normalized' || field === 'threshold') {
            convertedParameters[parameterType][field] = value;
          } else if (conversionType && value) {
            convertedParameters[parameterType][field] = convert(value, conversionType, fromSystem, toSystem).toString();
          } else {
            convertedParameters[parameterType][field] = value;
          }
        });
      } else {
        convertedParameters[parameterType] = parameters[parameterType];
      }
    });

    return convertedParameters;
  }, [convert]);

  /**
   * Get the unit label for a specific parameter type in the current unit system
   * @param {string} parameterType - Type of parameter ('pressure', 'flow', 'conductivity', 'temperature')
   * @returns {string} Unit label
   */
  const getUnit = useCallback((parameterType) => {
    return getUnitLabel(parameterType, unitSystem);
  }, [unitSystem]);

  /**
   * Convert validation limits for the current unit system
   * @param {Object} limits - Validation limits object
   * @param {string} parameterType - Type of parameter
   * @param {string} fromSystem - Source unit system (defaults to Metric)
   * @returns {Object} Converted validation limits
   */
  const convertLimits = useCallback((limits, parameterType, fromSystem = UNIT_SYSTEMS.METRIC) => {
    return convertValidationLimits(limits, parameterType, fromSystem, unitSystem);
  }, [unitSystem]);

  /**
   * Change the unit system and optionally convert existing data
   * @param {string} newUnitSystem - The new unit system to switch to
   * @param {Object} options - Options for data conversion
   * @param {Object} options.formData - Form data to convert
   * @param {Object} options.parameters - Parameters to convert
   * @param {Function} options.onFormDataConverted - Callback for converted form data
   * @param {Function} options.onParametersConverted - Callback for converted parameters
   */
  const changeUnitSystem = useCallback((newUnitSystem, options = {}) => {
    const { formData, parameters, onFormDataConverted, onParametersConverted } = options;
    
    if (newUnitSystem === unitSystem) return;

    const oldUnitSystem = unitSystem;
    setUnitSystem(newUnitSystem);

    // Convert existing data if provided
    if (formData && onFormDataConverted) {
      const convertedFormData = convertFormData(formData, oldUnitSystem, newUnitSystem);
      onFormDataConverted(convertedFormData);
    }

    if (parameters && onParametersConverted) {
      const convertedParameters = convertParameters(parameters, oldUnitSystem, newUnitSystem);
      onParametersConverted(convertedParameters);
    }
  }, [unitSystem, convertFormData, convertParameters]);

  /**
   * Get all available unit labels for the current system
   */
  const unitLabels = useMemo(() => ({
    pressure: getUnit('pressure'),
    flow: getUnit('flow'),
    conductivity: getUnit('conductivity'),
    temperature: getUnit('temperature')
  }), [getUnit]);

  /**
   * Check if the current unit system is metric
   */
  const isMetric = useMemo(() => unitSystem === UNIT_SYSTEMS.METRIC, [unitSystem]);

  /**
   * Check if the current unit system is US
   */
  const isUS = useMemo(() => unitSystem === UNIT_SYSTEMS.US, [unitSystem]);

  return {
    // State
    unitSystem,
    isMetric,
    isUS,
    
    // Unit labels
    unitLabels,
    getUnit,
    
    // Conversion functions
    convert,
    convertFormData,
    convertParameters,
    convertLimits,
    
    // Unit system management
    changeUnitSystem,
    setUnitSystem
  };
};
