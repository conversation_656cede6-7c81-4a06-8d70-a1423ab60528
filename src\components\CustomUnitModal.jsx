import { useState, useEffect } from 'react';
import { Mo<PERSON>, Button, Alert, Row, Col } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import OptionSelector from './OptionSelector';

/**
 * Custom Unit Modal Component
 * Allows users to select between US and Metric units for different parameters
 */
export default function CustomUnitModal({
  visible,
  onCancel,
  onSave,
  initialUnits = {}
}) {
  const [selectedUnits, setSelectedUnits] = useState({
    flow: initialUnits.flow || 'm³/h',
    pressure: initialUnits.pressure || 'bar',
    temperature: initialUnits.temperature || '°C',
    conductivity: initialUnits.conductivity || 'μS/cm'
  });

  const [errors, setErrors] = useState({});

  // Reset form when modal opens
  useEffect(() => {
    if (visible) {
      setSelectedUnits({
        flow: initialUnits.flow || 'm³/h',
        pressure: initialUnits.pressure || 'bar',
        temperature: initialUnits.temperature || '°C',
        conductivity: initialUnits.conductivity || 'μS/cm'
      });
      setErrors({});
      // form.resetFields();
    }
  }, [visible, initialUnits]);

  const validateUnits = () => {
    const newErrors = {};
    
    // Ensure all required units are selected
    if (!selectedUnits.flow) newErrors.flow = 'Flow unit is required';
    if (!selectedUnits.pressure) newErrors.pressure = 'Pressure unit is required';
    if (!selectedUnits.temperature) newErrors.temperature = 'Temperature unit is required';
    if (!selectedUnits.conductivity) newErrors.conductivity = 'Conductivity unit is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUnitChange = (parameterType, unit) => {
    setSelectedUnits(prev => ({
      ...prev,
      [parameterType]: unit
    }));

    // Clear error for this field if it exists
    if (errors[parameterType]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[parameterType];
        return newErrors;
      });
    }
  };

  const handleSave = () => {
    if (validateUnits()) {
      onSave(selectedUnits);
    }
  };

  const unitGroups = [
    {
      key: 'flow',
      label: 'Flow',
      options: [
        { value: 'gpm', label: 'gpm' },
        { value: 'm³/h', label: 'm³/h' },

      ]
    },
    {
      key: 'flow',
      label: '',
      options: [
        { value: 'gpd', label: 'gpd' },
        { value: 'm³/d', label: 'm³/d' }
      ]
    },
    {
      key: 'pressure',
      label: 'Pressure',
      options: [
        { value: 'psi', label: 'Psi' },
        { value: 'bar', label: 'bar' }
      ]
    },
    {
      key: 'temperature',
      label: 'Temperature',
      options: [
        { value: '°F', label: '°F' },
        { value: '°C', label: '°C' }
      ]
    },
    {
      key: 'conductivity',
      label: 'Salt Content',
      options: [
        { value: 'μS/cm', label: 'μS/cm' },
        { value: 'mg/L', label: 'mg/L' }
      ]
    }
  ];

  return (
    <Modal
      open={visible}
      onCancel={onCancel}
      width={450}
      footer={null}
      title="Custom Units"
      className="custom-units-modal-wrapper"
      closeIcon={<CloseOutlined />}
    >
      <div className="custom-units-modal">
        {Object.keys(errors).length > 0 && (
          <Alert
            message="Please correct the following errors:"
            type="error"
            showIcon
            description={
              <ul>
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>{error}</li>
                ))}
              </ul>
            }
            className="error-alert"
            style={{ marginBottom: '16px' }}
          />
        )}

        <div className="custom-units-grid">
            <Row className="custom-unit-header-row">
            <Col span={6}></Col>
            <Col span={18}>
              <div className="custom-unit-headers">
                <span className="unit-header us">US</span>
                <span className="unit-header metric">Metric</span>
              </div>
            </Col>
          </Row>
          {/* <div style={{ display: 'flex', gap: '60px', marginBottom: '5px' }}><span>US</span> <span>Metric</span></div> */}
          {unitGroups.map((group, index) => (
            <Row 
              key={`${group.key}-${index}`} 
              className={`custom-unit-row ${group.label === '' ? 'custom-unit-flow-2-row' : ''}`} 
              align="middle"
            >
              <Col span={6} className="custom-unit-label-col">
                <span className="custom-unit-label">
                  {group.label === '' && group.key === 'flow' ? null : group.label}
                </span>
              </Col>
              <Col span={18} className="custom-unit-options-col">
                <div className="custom-unit-buttons-wrapper">
                  <OptionSelector
                    options={group.options}
                    selectedOption={selectedUnits[group.key]}
                    onSelect={value => handleUnitChange(group.key, value)}
                    className="custom-unit-selector"
                    disabled={false}
                    size="small"
                  />
                  {/* {group.options.map(option => (
                    <button
                      key={option.value}
                      className={`custom-unit-button ${selectedUnits[group.key] === option.value ? 'selected' : ''}`}
                      onClick={() => handleUnitChange(group.key, option.value)}
                      type="button"
                    >
                      {option.label}
                    </button>
                  ))} */}
                </div>
                {errors[group.key] && (
                  <div className="custom-unit-error">{errors[group.key]}</div>
                )}
              </Col>
            </Row>
          ))}
        </div>

        <div className="custom-units-footer">
          <Button
            type="primary"
            onClick={handleSave}
            className="save-units-button"
            size="large"
          >
            Save Units
          </Button>
        </div>
      </div>
    </Modal>
  );
}

CustomUnitModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  initialUnits: PropTypes.object
};


