import { UNIT_SYSTEMS, getUnitLabel } from '../utils/unitConversion';

/**
 * Configuration for main form input fields with unit-specific validation limits
 * Follows the same pattern as parameterConfig.js for consistency
 */

// Form field types
export const FORM_FIELD_TYPES = {
  TEMPERATURE: 'temperature',
  FEED_FLOW: 'feedFlow',
  FEED_CONDUCTIVITY: 'feedConductivity',
};

// Form field configuration with validation limits for different unit systems
export const FORM_FIELD_CONFIG = [
  {
    type: FORM_FIELD_TYPES.TEMPERATURE,
    label: 'Temperature',
    parameterType: 'temperature', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        min: 0,
        max: 100, // Celsius
      },
      [UNIT_SYSTEMS.US]: {
        min: 32,
        max: 212, // Fahrenheit (0°C = 32°F, 100°C = 212°F)
      }
    }
  },
  {
    type: FORM_FIELD_TYPES.FEED_FLOW,
    label: 'Feed Flow',
    parameterType: 'flow', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        min: 0,
        max: 12000, // m³/d (500 m³/h * 24)
      },
      [UNIT_SYSTEMS.US]: {
        min: 0,
        max: 3170400, // gpd (~500 m³/h * 24 * 264.172)
      }
    }
  },
  {
    type: FORM_FIELD_TYPES.FEED_CONDUCTIVITY,
    label: 'Feed Conductivity',
    parameterType: 'conductivity', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        min: 0.1,
        max: 1000000, // µS/cm
      },
      [UNIT_SYSTEMS.US]: {
        min: 0.1,
        max: 1000000, // ppm (same range for conductivity)
      }
    }
  }
];

/**
 * Get form field configuration by type
 * @param {string} fieldType - The form field type
 * @returns {Object|null} Field configuration or null if not found
 */
export const getFormFieldConfig = (fieldType) => {
  return FORM_FIELD_CONFIG.find(config => config.type === fieldType) || null;
};

/**
 * Get validation limits for a specific form field in a specific unit system
 * with dynamic conversion for custom units
 *
 * @param {string} fieldType - The form field type (temperature, feedFlow, feedConductivity)
 * @param {string} unitSystem - The unit system ('Metric', 'US', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {Object|null} Validation limits with min, max, and unit
 */
export const getFormFieldValidation = (fieldType, unitSystem, customUnits = {}) => {
  const config = getFormFieldConfig(fieldType);
  if (!config) return null;

  // Get the parameter conversion type (temperature, flow, conductivity)
  const conversionType = config.parameterType;

  // For standard unit systems, use predefined validation
  if (unitSystem === UNIT_SYSTEMS.METRIC || unitSystem === UNIT_SYSTEMS.US) {
    const validation = config.validation[unitSystem];
    if (!validation) return null;

    return {
      ...validation,
      unit: getUnitLabel(conversionType, unitSystem)
    };
  }

  // For custom unit system, convert from base metric values
  if (unitSystem === UNIT_SYSTEMS.CUSTOM && customUnits[conversionType]) {
    const baseValidation = config.validation[UNIT_SYSTEMS.METRIC];
    if (!baseValidation) return null;

    const customUnit = customUnits[conversionType];
    const conversionFactor = getConversionFactor(conversionType, customUnit);

    if (conversionFactor) {
      return {
        min: Math.round(baseValidation.min * conversionFactor * 100) / 100,
        max: Math.round(baseValidation.max * conversionFactor * 100) / 100,
        unit: customUnit
      };
    }
  }

  // Fallback to metric if custom conversion fails
  const fallbackValidation = config.validation[UNIT_SYSTEMS.METRIC];
  return fallbackValidation ? {
    ...fallbackValidation,
    unit: getUnitLabel(conversionType, UNIT_SYSTEMS.METRIC)
  } : null;
};

/**
 * Get conversion factor for custom units
 * @param {string} conversionType - The conversion type (temperature, flow, conductivity)
 * @param {string} customUnit - The custom unit
 * @returns {number|null} Conversion factor or null
 */
const getConversionFactor = (conversionType, customUnit) => {
  const conversionFactors = {
    temperature: {
      '°F': 1.8, // °C to °F conversion (plus 32 offset handled separately)
      '°C': 1,
    },
    flow: {
      'gpd': 264.172052358148, // m³/d to gpd
      'gpm': 0.183454, // m³/d to gpm (m³/d / 24 * 4.4029)
      'm³/d': 1,
      'm³/h': 0.041667, // m³/d to m³/h (1/24)
    },
    conductivity: {
      'mg/L': 1, // Same scale for conductivity
      'µS/cm': 1,
    }
  };

  return conversionFactors[conversionType]?.[customUnit] || null;
};

/**
 * Get all form field types for iteration
 * @returns {Array} Array of form field type strings
 */
export const getAllFormFieldTypes = () => {
  return Object.values(FORM_FIELD_TYPES);
};

/**
 * Check if a form field type exists in configuration
 * @param {string} fieldType - The form field type to check
 * @returns {boolean} True if field type exists
 */
export const isValidFormFieldType = (fieldType) => {
  return Object.values(FORM_FIELD_TYPES).includes(fieldType);
};
