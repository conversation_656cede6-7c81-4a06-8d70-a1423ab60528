// Simple test to verify imports are working correctly
import { getParameterFieldValidation } from '../config/parameterConfig.js';
import { convertValidationLimits, determineBaseUnitSystem } from '../utils/unitConversion.js';
import { UNIT_SYSTEMS } from '../utils/unitConversion.js';

console.log('Testing imports...');

// Test getParameterFieldValidation
try {
  const validation = getParameterFieldValidation('feedPressure', 'start', UNIT_SYSTEMS.METRIC);
  console.log('✅ getParameterFieldValidation works:', validation);
} catch (error) {
  console.error('❌ getParameterFieldValidation failed:', error);
}

// Test determineBaseUnitSystem
try {
  const baseSystem = determineBaseUnitSystem('pressure', 'psi');
  console.log('✅ determineBaseUnitSystem works:', baseSystem);
} catch (error) {
  console.error('❌ determineBaseUnitSystem failed:', error);
}

// Test convertValidationLimits
try {
  const limits = { min: 0, max: 100 };
  const converted = convertValidationLimits(limits, 'pressure', UNIT_SYSTEMS.METRIC, UNIT_SYSTEMS.US);
  console.log('✅ convertValidationLimits works:', converted);
} catch (error) {
  console.error('❌ convertValidationLimits failed:', error);
}

console.log('Import test completed!');
