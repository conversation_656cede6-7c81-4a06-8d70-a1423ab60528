import PropTypes from 'prop-types';
import { DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import { DEFAULT_VALUES } from '../../constants';
import './DateInput.css';

export default function DateInput({
  label,
  name,
  onChange,
  disabled = false,
  placeholder = DEFAULT_VALUES.PLACEHOLDER_DATE,
  rules = [],
  required = false,
}) {
  const handleDateChange = (_, dateString) => {
    if (onChange) {
      // Pass the formatted string to match existing data format
      onChange(dateString || '');
    }
  };

  return (
    <div className="input-container">
      {label && <label className="input-label">{label}</label>}
      <Form.Item
        name={name}
        rules={[
          ...(required
            ? [{ required: true, message: `${label} is required` }]
            : []),
          ...rules,
        ]}
        validateTrigger={['onChange', 'onBlur']}
        getValueFromEvent={(_, dateString) => dateString || ''}
        getValueProps={value => ({
          value:
            value && typeof value === 'string' && value.trim()
              ? (() => {
                  const formats = [
                    'MM/DD/YYYY',
                    'M/D/YYYY',
                    'M/DD/YYYY',
                    'MM/D/YYYY',
                  ];
                  for (const format of formats) {
                    const parsedDate = dayjs(value, format, true);
                    if (parsedDate.isValid()) {
                      return parsedDate;
                    }
                  }
                  return null;
                })()
              : null,
        })}
        // help="MM/DD/YYYY"
      >
        <DatePicker
          onChange={handleDateChange}
          disabled={disabled}
          format="MM/DD/YYYY"
          placeholder={placeholder}
          size="small"
          className="date-picker"
          allowClear={false}
          nextIcon={<i className="fas fa-angle-right" />}
          prevIcon={<i className="fas fa-angle-left" />}
          superNextIcon={<i className="fas fa-angle-double-right" />}
          superPrevIcon={<i className="fas fa-angle-double-left" />}

          style={{
            width: '100%',
            height: '32px',
            borderRadius: '4px',
          }}
        />
      </Form.Item>
      {/* <p className="input-help-text">MM/DD/YYYY</p> */}
    </div>
  );
}

DateInput.propTypes = {
  label: PropTypes.string,
  name: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  rules: PropTypes.arrayOf(PropTypes.object),
  required: PropTypes.bool,
};
