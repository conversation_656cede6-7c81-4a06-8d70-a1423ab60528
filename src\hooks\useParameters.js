import { useState, useCallback } from 'react';
import { PARAMETER_TYPES } from '../constants';

// Initial parameters data
const INITIAL_PARAMETERS = {
  [PARAMETER_TYPES.FEED_PRESSURE]: {
    start: '65',
    current: '70',
    normalized: '60',
    limit: '95',
    threshold: ''
  },
  [PARAMETER_TYPES.DP]: {
    start: '0.50',
    current: '0.60',
    normalized: '0.57',
    limit: '2.00',
    threshold: '3%'
  },
  [PARAMETER_TYPES.Q_PERM]: {
    start: '100',
    current: '90',
    normalized: '105',
    limit: '20',
    threshold: '10%'
  },
  [PARAMETER_TYPES.COND_PERM]: {
    start: '200',
    current: '180',
    normalized: '209',
    limit: '1000',
    threshold: '10%'
  }
};

/**
 * Custom hook for managing parameters state
 * @returns {Object} Parameters state and update functions
 */
export const useParameters = () => {
  const [parameters, setParameters] = useState(INITIAL_PARAMETERS);

  /**
   * Update a specific parameter field
   * @param {string} parameterType - The parameter type
   * @param {string} field - The field name (start, current, normalized, limit, threshold)
   * @param {string} value - The new value
   */
  const updateParameter = useCallback((parameterType, field, value) => {
    setParameters(prev => ({
      ...prev,
      [parameterType]: {
        ...prev[parameterType],
        [field]: value
      }
    }));
  }, []);

  /**
   * Update multiple fields for a parameter
   * @param {string} parameterType - The parameter type
   * @param {Object} updates - Object with field-value pairs
   */
  const updateParameterFields = useCallback((parameterType, updates) => {
    setParameters(prev => ({
      ...prev,
      [parameterType]: {
        ...prev[parameterType],
        ...updates
      }
    }));
  }, []);

  /**
   * Reset parameters to initial values
   */
  const resetParameters = useCallback(() => {
    setParameters(INITIAL_PARAMETERS);
  }, []);

  /**
   * Get a specific parameter
   * @param {string} parameterType - The parameter type
   * @returns {Object} The parameter data
   */
  const getParameter = useCallback((parameterType) => {
    return parameters[parameterType];
  }, [parameters]);

  /**
   * Get all parameters as an array with metadata
   * @returns {Array} Array of parameter objects with metadata
   */
  const getParametersArray = useCallback(() => {
    return Object.entries(parameters).map(([key, value]) => ({
      type: key,
      ...value,
      label: getParameterLabel(key),
      unit: getParameterUnit(key)
    }));
  }, [parameters]);

  return {
    parameters,
    setParameters,
    updateParameter,
    updateParameterFields,
    resetParameters,
    getParameter,
    getParametersArray
  };
};

/**
 * Get human-readable label for parameter type
 * @param {string} parameterType - The parameter type
 * @returns {string} Human-readable label
 */
const getParameterLabel = (parameterType) => {
  const labels = {
    [PARAMETER_TYPES.FEED_PRESSURE]: 'Feed Pressure',
    [PARAMETER_TYPES.DP]: 'dP',
    [PARAMETER_TYPES.Q_PERM]: 'Q perm',
    [PARAMETER_TYPES.COND_PERM]: 'Cond perm'
  };
  return labels[parameterType] || parameterType;
};

/**
 * Get unit for parameter type
 * @param {string} parameterType - The parameter type
 * @returns {string} Unit string
 */
const getParameterUnit = (parameterType) => {
  const units = {
    [PARAMETER_TYPES.FEED_PRESSURE]: 'bar',
    [PARAMETER_TYPES.DP]: 'bar',
    [PARAMETER_TYPES.Q_PERM]: 'm³/h',
    [PARAMETER_TYPES.COND_PERM]: 'μS/cm'
  };
  return units[parameterType] || '';
};
