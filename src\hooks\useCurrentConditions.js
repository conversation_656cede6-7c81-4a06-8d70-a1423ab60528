import { useState, useEffect } from 'react';
import { STATUS_TYPES } from '../constants';
import { getStatusCardIcon } from '../utils/iconUtils';

/**
 * Hook to manage current conditions data and loading state
 */
export const useCurrentConditions = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [conditions, setConditions] = useState([]);

  // Simulate loading and data fetching
  useEffect(() => {
    const loadConditions = async () => {
      setIsLoading(true);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Set the actual conditions data
      const conditionsData = [
        {
          id: 'feedPressure',
          title: 'Feed Pressure',
          value: '70 bar',
          status: STATUS_TYPES.NORMAL,
          estimatedLife: true,
          date: 'Nov 24',
          months: '8',
          iconSrc: getStatusCardIcon('feedPressure')
        },
        {
          id: 'dP',
          title: 'dP',
          value: '0.60 bar',
          status: STATUS_TYPES.WARNING,
          estimatedLife: true,
          date: 'Apr 26',
          months: '8',
          iconSrc: getStatusCardIcon('dP')
        },
        {
          id: 'qPerm',
          title: 'Q Perm',
          value: '90 m³/h',
          status: STATUS_TYPES.NORMAL,
          estimatedLife: true,
          date: 'Nov 25',
          months: '8',
          iconSrc: getStatusCardIcon('qPerm')
        },
        {
          id: 'condPerm',
          title: 'Cond Perm',
          value: '180 μS/cm',
          status: STATUS_TYPES.NORMAL,
          estimatedLife: true,
          date: 'Nov 24',
          months: '8',
          iconSrc: getStatusCardIcon('condPerm')
        }
      ];
      
      setConditions(conditionsData);
      setIsLoading(false);
    };

    loadConditions();
  }, []);

  const recalculate = async () => {
    setIsLoading(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsLoading(false);
  };

  return {
    isLoading,
    conditions,
    setIsLoading,
    recalculate
  };
};
