# RoAdvisorLite Project Documentation

## Overview
RoAdvisorLite is a React-based web application for Reverse Osmosis (RO) membrane performance analysis and forecasting. It provides a comprehensive interface for calculating and monitoring RO system conditions, membrane performance, and operational parameters with advanced custom unit support.

## Technology Stack
- **Frontend Framework**: React 19.1.0 with Vite 6.3.5
- **UI Library**: Ant Design (antd) 5.26.2 with @ant-design/icons
- **Date Handling**: MUI X Date Pickers 8.6.0, dayjs 1.11.13, moment 2.30.1
- **Styling**: CSS with Tailwind CSS 4.1.8, PostCSS, Autoprefixer
- **Development**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, lint-staged
- **Build Tool**: Vite with React plugin
- **State Management**: React Context API with performance optimization

## Project Structure

### Core Application (`src/`)
```
src/
├── App.jsx                 # Main application component
├── App.css                 # Global application styles
├── main.jsx               # Application entry point
├── index.css              # Global CSS variables and base styles
└── calculation.js         # Core calculation functions
```

### Component Architecture (`src/components/`)
```
components/
├── ButtonGroup.jsx         # Reusable button group component
├── CIPSection.jsx         # Clean-in-Place section component
├── CustomUnitModal.jsx    # Modal for custom unit configuration
├── DateInput/             # Date input component with calendar
├── NumericInput.jsx       # Numeric input with validation and units
├── OptionSelector.jsx     # Generic option selector component
├── ParameterTable/        # Parameter table with calculations
├── StatusCard.jsx         # Status display cards
└── examples/              # Component usage examples
```

### State Management (`src/contexts/`)
Centralized form state management using React Context API:
```
contexts/
├── FormContext.jsx        # Context definitions and types
├── FormProvider.jsx       # Main provider component
├── FormDataProvider.jsx   # Data state provider
├── FormActionsProvider.jsx # Actions provider
├── FormValidationProvider.jsx # Validation provider
├── useFormContext.js      # Context access hooks
└── index.js              # Exports and API
```

### Custom Hooks (`src/hooks/`)
```
hooks/
├── useCIPData.js          # Clean-in-Place data management
├── useCurrentConditions.js # Current conditions calculations
├── useCustomUnits.js      # Custom unit system management
├── useParameters.js       # Parameter table data
└── useUnitConverter.js    # Unit conversion utilities
```

### Configuration (`src/config/`)
```
config/
├── parameterConfig.js     # Parameter definitions and metadata
└── statusCardData.js      # Status card configuration
```

### Constants (`src/constants/`)
```
constants/
├── fieldMetadata.js       # Field validation and metadata
└── index.js              # Application constants
```

### Utilities (`src/utils/`)
```
utils/
├── calculations/          # Calculation utilities
├── validation/           # Validation system
├── dateUtils.js          # Date handling utilities
├── iconUtils.js          # Icon management
├── statusUtils.js        # Status determination logic
└── unitConversion.js     # Unit conversion functions
```

## Key Features

### 1. Advanced Form Management System
- **Centralized State**: React Context-based form state management with FormDataProvider
- **Context Splitting**: Separate contexts for data, actions, and validation for performance
- **Performance Optimization**: Memoized providers and selective re-renders
- **Type Safety**: PropTypes validation throughout
- **Real-time Synchronization**: Immediate updates across all components

### 2. Comprehensive Unit System Support
- **Multiple Systems**: Metric, US, and Custom units with full conversion support
- **Dynamic Conversion**: Real-time unit conversion with preserved values and relationships
- **Custom Units**: User-configurable unit preferences with modal interface
- **Validation Integration**: Unit-aware validation rules with dynamic constraints
- **Persistence**: Custom unit selections saved to localStorage
- **Parameter Table Integration**: Full custom unit support in parameter calculations

### 3. Advanced Parameter Calculations
- **Normalized Values**: Temperature and pressure normalized calculations (NFP, NFR, NQPerm, NCondPerm)
- **Real-time Updates**: Automatic recalculation on input changes
- **Threshold Monitoring**: CIP threshold calculations with status indicators
- **Status Determination**: Color-coded status indicators (green/yellow/red)
- **Unit Conversion**: Automatic parameter value conversion between unit systems

### 4. Robust Validation System
- **Field-level Validation**: Individual input validation with custom unit support
- **Dynamic Constraints**: Unit-system aware min/max values that adapt to custom units
- **Error Messaging**: Clear, contextual error messages with unit-specific limits
- **Disabled Field Handling**: No validation for calculated/normalized fields
- **Custom Unit Validation**: Proper validation limits based on underlying unit system

### 5. Modern UI Components
- **Ant Design Integration**: Consistent design system with custom theming
- **Responsive Layout**: Mobile-friendly grid system with proper breakpoints
- **Accessibility**: ARIA labels and keyboard navigation support
- **Custom Styling**: Brand-consistent color scheme with CSS variables
- **Interactive Elements**: Custom unit modal, date pickers, numeric inputs with units

## Data Flow

### 1. Input Processing
```
User Input → NumericInput → Form Context → Validation → State Update → UI Refresh
```

### 2. Calculations
```
Form Data → Parameter Calculations → Normalized Values → Status Determination → Display Update
```

### 3. Unit Conversion Flow
```
Unit Change → Form Context → Convert Form Data → Convert Parameters → Update Labels → UI Refresh
```

### 4. Custom Units Flow
```
Custom Modal → Save Units → Update Context → Convert Values → Refresh Labels → Persist to Storage
```

### 5. Parameter Table Integration
```
Form Context → Parameter Config → Unit Labels → Validation Limits → Real-time Updates
```

## Key Calculations

### Normalized Feed Pressure (NFP)
```javascript
calculateNFP(feedPressure, flowRate, pressureDrop, feedConductivity, initialPressure, temperature)
// Accounts for temperature and flow rate variations
```

### Normalized Flow Rate (NFR)
```javascript
calculateNFR(flowRate, initialQPerm, currentQPerm, initialTemperature, currentTemperature)
// Temperature-corrected flow rate normalization
```

### Normalized Permeate Quality (NQPerm)
```javascript
calculateNQPerm(currentQPerm, initialTemperature, currentTemperature)
// Temperature-corrected permeate quality
```

### Normalized Conductivity Permeate (NCondPerm)
```javascript
calculateNCondPerm(currentCPerm, initialTemperature, currentTemperature)
// Temperature-corrected conductivity normalization
```

### Unit Conversion Functions
```javascript
// Generic conversion with custom unit support
convertValue(value, parameterType, fromSystem, toSystem, fromCustomUnits, toCustomUnits)

// Specific unit conversions
convertBetweenCustomUnits(value, parameterType, fromUnit, toUnit)
convertFlowBetweenUnits(value, fromUnit, toUnit) // Supports gpm, gpd, m³/h, m³/d
convertConductivityBetweenUnits(value, fromUnit, toUnit) // Supports mg/L, ppm, μS/cm
```

## Application Sections

### 1. Header Section
- Application title and description
- Membrane type selection (RO/NF)
- Unit system selection with custom units support
- CIP frequency and duration configuration

### 2. Data Input Grid (Three-Column Layout)
- **Start Conditions**: Initial operating parameters with date, temperature, flow, conductivity
- **Current Conditions**: Present operating state with same parameter types
- **Forecast Conditions**: Predicted future state for trend analysis
- **Real-time Unit Labels**: All inputs show current unit system labels
- **Custom Unit Support**: Immediate reflection of custom unit selections

### 3. Advanced Parameter Table
- **Editable Parameters**: Feed Pressure, dP, Q Perm, Cond Perm with custom unit labels
- **Calculated Values**: Normalized values (NFP, NFR, NQPerm, NCondPerm) in disabled fields
- **CIP Thresholds**: Automatic threshold calculations with status indicators
- **Status Color Coding**: Green (good), Yellow (warning), Red (action needed)
- **Custom Unit Integration**: Full support for custom units in all parameter types
- **Real-time Conversion**: Automatic value conversion when switching unit systems

### 4. Output Section
- **Status Cards**: Current condition indicators with color-coded status
- **Performance Metrics**: Key performance indicators and trends
- **Maintenance Alerts**: CIP recommendations and timing
- **Membrane Life Estimation**: Predictive maintenance scheduling

### 5. Custom Unit Modal
- **Unit Selection Interface**: Choose custom units for each parameter type
- **Real-time Preview**: See unit changes immediately
- **Persistence**: Custom selections saved across sessions
- **Validation**: Ensures compatible unit selections

## Custom Units System

### Architecture
- **Centralized Management**: Custom units handled through useCustomUnits hook
- **Form Context Integration**: Custom units passed through FormProvider to all components
- **Real-time Synchronization**: Immediate updates across form fields and parameter table
- **Persistence**: localStorage integration for session persistence

### Supported Unit Types
```javascript
// Pressure Units
['bar', 'psi']

// Flow Units
['m³/h', 'm³/d', 'gpm', 'gpd']

// Conductivity Units
['mg/L', 'ppm', 'μS/cm']

// Temperature Units
['°C', '°F']
```

### Conversion Factors
```javascript
CONVERSION_FACTORS = {
  PRESSURE: { bar_to_psi: 14.5038 },
  FLOW: { m3h_to_gpm: 4.4029, m3h_to_m3d: 24, gpm_to_gpd: 1440 },
  CONDUCTIVITY: { uScm_to_ppm: 0.5, uScm_to_mgL: 0.5 },
  TEMPERATURE: { celsius_to_fahrenheit: (c) => (c * 9/5) + 32 }
}
```

### Integration Points
- **NumericInput Components**: Dynamic unit labels via addonAfter
- **Parameter Table**: Custom unit labels and validation
- **Form Validation**: Unit-aware min/max constraints
- **Value Conversion**: Automatic conversion between unit systems

## Styling System

### CSS Variables
```css
:root {
  --brand-500: #1890ff;
  --gray-500: #8c8c8c;
  --white: #ffffff;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --font-size-base: 14px;
}
```

### Component Patterns
- Fixed input dimensions (200px width, 32px height)
- Consistent spacing and alignment
- Ant Design grid system integration
- Responsive breakpoints
- Custom unit label styling

## Development Guidelines

### Component Creation
1. **Follow Established Patterns**: Use ButtonGroup and NumericInput as reference examples
2. **PropTypes Validation**: Include comprehensive PropTypes for all components
3. **Ant Design Integration**: Leverage Ant Design components for consistency
4. **Responsive Design**: Implement mobile-friendly layouts with proper breakpoints
5. **Accessibility**: Add ARIA labels, keyboard navigation, and screen reader support
6. **Custom Units Support**: Ensure new components integrate with custom units system

### State Management Best Practices
1. **Form Context Usage**: Use form context for all form-related state management
2. **Local State**: Reserve for component-specific UI state only
3. **Custom Hooks**: Extract complex logic into reusable custom hooks
4. **Performance Optimization**: Use useMemo and useCallback for expensive operations
5. **Context Splitting**: Separate concerns (data, actions, validation) for better performance

### Validation Implementation
1. **Field-level Validation**: Implement validation at the field level with clear error messages
2. **Unit-system Awareness**: Ensure validation constraints adapt to current unit system
3. **Custom Unit Support**: Validation limits should work with custom units
4. **Disabled Field Handling**: Skip validation for calculated/normalized fields
5. **Real-time Feedback**: Provide immediate validation feedback on user input

### Custom Units Integration
1. **Context Integration**: Always access custom units through form context
2. **Unit Label Updates**: Ensure unit labels update immediately when custom units change
3. **Value Conversion**: Implement proper value conversion when switching unit systems
4. **Validation Updates**: Update validation constraints when custom units change
5. **Persistence**: Ensure custom unit selections persist across page reloads

### Styling Standards
1. **CSS Variables**: Use defined CSS variables for consistent theming
2. **Ant Design Patterns**: Follow Ant Design design system guidelines
3. **Responsive Behavior**: Maintain functionality across all device sizes
4. **Brand Consistency**: Preserve established color scheme and visual identity
5. **Component Dimensions**: Follow standard input dimensions (200px width, 32px height)

## Build and Development

### Scripts
```bash
npm run dev      # Development server
npm run build    # Production build
npm run lint     # ESLint checking
npm run preview  # Preview production build
```

### Code Quality
- ESLint configuration with React rules
- Prettier for code formatting
- Husky for pre-commit hooks
- lint-staged for staged file linting

## Recent Major Updates

### Custom Units System Integration (Latest)
- **Complete Custom Units Support**: Full integration across all form fields and parameter table
- **Real-time Synchronization**: Immediate updates when custom units are changed
- **Advanced Unit Conversion**: Support for complex unit conversions (gpd, m³/d, etc.)
- **Parameter Table Integration**: Custom units fully supported in parameter calculations
- **Validation Enhancement**: Dynamic validation limits based on custom unit selections
- **Persistence**: Custom unit preferences saved to localStorage

### Form Context System Enhancement
- **Performance Optimization**: Context splitting for better re-render performance
- **Custom Units Integration**: Form context now manages custom units state
- **Enhanced Validation**: Unit-aware validation with custom unit support
- **Real-time Updates**: Immediate propagation of changes across all components

## Architecture Highlights

### Form Context System
```javascript
// Three-layer context architecture
FormProvider
├── FormDataProvider (data state)
├── FormActionsProvider (actions & conversions)
└── FormValidationProvider (validation logic)

// Custom hooks for easy access
useFormData() // Access form data, parameters, custom units
useFormActions() // Access update functions, unit conversion
useFormValidation() // Access validation state and functions
```

### Custom Units Flow
```javascript
// Custom units integration flow
CustomUnitModal → useCustomUnits → FormProvider → FormDataProvider →
All Components (NumericInput, ParameterTable) → Real-time Updates
```

### Unit Conversion Architecture
```javascript
// Centralized conversion system
convertValue(value, parameterType, fromSystem, toSystem, fromCustomUnits, toCustomUnits)
├── Standard conversions (Metric ↔ US)
├── Custom unit conversions (any unit ↔ any unit)
├── Flow unit conversions (gpm, gpd, m³/h, m³/d)
└── Conductivity conversions (mg/L, ppm, μS/cm)
```

## Testing & Quality Assurance

### Current Testing Status
- **Manual Testing**: Comprehensive manual testing of all features
- **Unit Conversion Testing**: Verified conversion factors and calculations
- **Custom Units Testing**: Full integration testing across all components
- **Validation Testing**: Confirmed proper validation behavior with custom units

### Code Quality
- **ESLint Configuration**: Strict linting rules for code consistency
- **Prettier Integration**: Automatic code formatting
- **PropTypes Validation**: Runtime type checking for all components
- **Performance Monitoring**: Context optimization and memoization

## Future Enhancements
- **TypeScript Migration**: Enhanced type safety and developer experience
- **Unit Testing Suite**: Comprehensive testing with React Testing Library
- **Performance Monitoring**: Advanced performance tracking and optimization
- **Advanced Calculations**: Additional RO performance calculations
- **Export/Import**: Data export and configuration import functionality
- **Historical Data**: Trend analysis and historical data tracking
- **Mobile App**: React Native version for mobile devices
- **API Integration**: Backend integration for data persistence
