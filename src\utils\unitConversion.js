/**
 * Centralized unit conversion utility for RoAdvisorLite application
 * Provides conversion factors and functions for pressure, flow, conductivity, and temperature
 */

// Unit system types
export const UNIT_SYSTEMS = {
  US: 'US',
  METRIC: 'Metric',
  CUSTOM: 'Custom'
};

// Conversion factors (from metric to US units)
export const CONVERSION_FACTORS = {
  PRESSURE: {
    bar_to_psi: 14.5038,
    psi_to_bar: 0.068947572932
  },
  FLOW: {
    m3h_to_gpm: 4.4029,
    m3h_to_m3d: 24,
    gpm_to_gpd: 1440,
    m3d_to_gpd: 264.172052358148
  },
  CONDUCTIVITY: {
    uScm_to_ppm: 0.5,
    uScm_to_mgL: 0.5, // Approximate conversion
    // mgL_to_ppm: 1.0,  // mg/L to ppm conversion
    // ppm_to_mgL: 1.0   // ppm to mg/L conversion
  },
  TEMPERATURE: {
    C_to_F: (c) => (c * 9 / 5) + 32,
    F_to_C: (f) => (f - 32) * 5 / 9
  }
};

// Unit labels for each system
export const UNIT_LABELS = {
  [UNIT_SYSTEMS.METRIC]: {
    pressure: 'bar',
    flow: 'm³/d',
    conductivity: 'mg/L',
    temperature: '°C'
  },
  [UNIT_SYSTEMS.US]: {
    pressure: 'psi',
    flow: 'gpd',
    conductivity: 'μS/cm',
    temperature: '°F'
  },
  [UNIT_SYSTEMS.CUSTOM]: {
    flow: {
      us: ['gpm', 'gpd'],
      metric: ['m³/h', 'm³/d'],
      defaultUS: 'gpm',
      defaultMetric: 'm³/d'
    },
    pressure: {
      us: 'psi',
      metric: 'bar'
    },
    conductivity: {
      us: 'μS/cm',
      metric: 'mg/L'
    },
    temperature: {
      us: '°F',
      metric: '°C'
    }
  }

};

/**
 * Convert pressure between bar and psi
 * @param {number} value - The value to convert
 * @param {string} fromSystem - Source unit system ('Metric' or 'US')
 * @param {string} toSystem - Target unit system ('Metric' or 'US')
 * @returns {number} Converted value
 */
export const convertPressure = (value, fromSystem, toSystem) => {
  if (!value || fromSystem === toSystem) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  if (fromSystem === UNIT_SYSTEMS.METRIC && toSystem === UNIT_SYSTEMS.US) {
    // bar to psi
    return numValue * CONVERSION_FACTORS.PRESSURE.bar_to_psi;
  } else if (fromSystem === UNIT_SYSTEMS.US && toSystem === UNIT_SYSTEMS.METRIC) {
    // psi to bar
    return numValue * CONVERSION_FACTORS.PRESSURE.psi_to_bar;
  }

  return value;
};

/**
 * Convert flow rate between m³/h and gpm
 * @param {number} value - The value to convert
 * @param {string} fromSystem - Source unit system ('Metric' or 'US')
 * @param {string} toSystem - Target unit system ('Metric' or 'US')
 * @returns {number} Converted value
 */
export const convertFlow = (value, fromSystem, toSystem) => {
  if (!value || fromSystem === toSystem) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  if (fromSystem === UNIT_SYSTEMS.METRIC && toSystem === UNIT_SYSTEMS.US) {
    // m³/d to gpd (metric default is now m³/d, US default is gpd)
    return numValue * CONVERSION_FACTORS.FLOW.m3d_to_gpd;
  } else if (fromSystem === UNIT_SYSTEMS.US && toSystem === UNIT_SYSTEMS.METRIC) {
    // gpd to m³/d
    return numValue / CONVERSION_FACTORS.FLOW.m3d_to_gpd;
  }

  return value;
};

/**
 * Convert conductivity between different units (μS/cm, ppm, mg/L)
 * @param {number} value - The value to convert
 * @param {string} fromSystem - Source unit system ('Metric' or 'US')
 * @param {string} toSystem - Target unit system ('Metric' or 'US')
 * @returns {number} Converted value
 */
export const convertConductivity = (value, fromSystem, toSystem) => {
  if (!value || fromSystem === toSystem) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  if (fromSystem === UNIT_SYSTEMS.METRIC && toSystem === UNIT_SYSTEMS.US) {
    // mg/L to μS/cm (approximate)
    return numValue / CONVERSION_FACTORS.CONDUCTIVITY.uScm_to_mgL;
  } else if (fromSystem === UNIT_SYSTEMS.US && toSystem === UNIT_SYSTEMS.METRIC) {
    // μS/cm to mg/L (approximate)
    return numValue * CONVERSION_FACTORS.CONDUCTIVITY.uScm_to_mgL;
  }

  return value;
};

/**
 * Convert conductivity between specific units
 * @param {number} value - The value to convert
 * @param {string} fromUnit - Source unit
 * @param {string} toUnit - Target unit
 * @returns {number} Converted value
 */
export const convertConductivityBetweenUnits = (value, fromUnit, toUnit) => {
  if (!value || fromUnit === toUnit) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  // Convert everything to μS/cm as base unit, then to target
  let valueInUScm = numValue;

  // Convert from source unit to μS/cm
  switch (fromUnit) {
    case 'ppm':
      valueInUScm = numValue / CONVERSION_FACTORS.CONDUCTIVITY.uScm_to_ppm;
      break;
    case 'mg/L':
      valueInUScm = numValue / CONVERSION_FACTORS.CONDUCTIVITY.uScm_to_mgL;
      break;
    case 'μS/cm':
      // Already in base unit
      break;
    default:
      console.warn(`Unknown conductivity unit: ${fromUnit}`);
      return value;
  }

  // Convert from μS/cm to target unit
  switch (toUnit) {
    case 'ppm':
      return valueInUScm * CONVERSION_FACTORS.CONDUCTIVITY.uScm_to_ppm;
    case 'mg/L':
      return valueInUScm * CONVERSION_FACTORS.CONDUCTIVITY.uScm_to_mgL;
    case 'μS/cm':
      return valueInUScm;
    default:
      console.warn(`Unknown conductivity unit: ${toUnit}`);
      return value;
  }
};

/**
 * Convert temperature between °C and °F
 * @param {number} value - The value to convert
 * @param {string} fromSystem - Source unit system ('Metric' or 'US')
 * @param {string} toSystem - Target unit system ('Metric' or 'US')
 * @returns {number} Converted value
 */
export const convertTemperature = (value, fromSystem, toSystem) => {
  if (!value || fromSystem === toSystem) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  if (fromSystem === UNIT_SYSTEMS.METRIC && toSystem === UNIT_SYSTEMS.US) {
    // °C to °F: °F = (°C × 9/5) + 32
    return CONVERSION_FACTORS.TEMPERATURE.C_to_F(numValue);
  } else if (fromSystem === UNIT_SYSTEMS.US && toSystem === UNIT_SYSTEMS.METRIC) {
    // °F to °C: °C = (°F - 32) × 5/9
    return CONVERSION_FACTORS.TEMPERATURE.F_to_C(numValue);
  }

  return value;
};

/**
 * Convert value between custom units and standard units
 * @param {number} value - The value to convert
 * @param {string} parameterType - Type of parameter ('pressure', 'flow', 'conductivity', 'temperature')
 * @param {string} fromUnit - Source unit (e.g., 'bar', 'psi', 'gpm', etc.)
 * @param {string} toUnit - Target unit (e.g., 'bar', 'psi', 'gpm', etc.)
 * @returns {number} Converted value
 */
export const convertBetweenCustomUnits = (value, parameterType, fromUnit, toUnit) => {
  if (!value || fromUnit === toUnit) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  // Map units to their system type
  const getUnitSystem = (unit) => {
    const metricUnits = {
      pressure: ['bar'],
      flow: ['m³/h', 'm³/d'],
      conductivity: ['mg/L'],
      temperature: ['°C']
    };

    const usUnits = {
      pressure: ['psi'],
      flow: ['gpm', 'gpd'],
      conductivity: ['ppm', 'μS/cm'],
      temperature: ['°F']
    };

    if (metricUnits[parameterType]?.includes(unit)) return UNIT_SYSTEMS.METRIC;
    if (usUnits[parameterType]?.includes(unit)) return UNIT_SYSTEMS.US;

    // Default fallback
    return UNIT_SYSTEMS.METRIC;
  };

  const fromSystem = getUnitSystem(fromUnit);
  const toSystem = getUnitSystem(toUnit);

  // Handle special unit conversions
  if (parameterType === 'flow') {
    return convertFlowBetweenUnits(numValue, fromUnit, toUnit);
  } else if (parameterType === 'conductivity') {
    return convertConductivityBetweenUnits(numValue, fromUnit, toUnit);
  }

  // Use standard conversion for other types
  return convertValue(numValue, parameterType, fromSystem, toSystem);
};

/**
 * Convert flow between different flow units (including gpd and m³/d)
 * @param {number} value - The value to convert
 * @param {string} fromUnit - Source unit
 * @param {string} toUnit - Target unit
 * @returns {number} Converted value
 */
export const convertFlowBetweenUnits = (value, fromUnit, toUnit) => {
  if (!value || fromUnit === toUnit) return value;

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return value;

  // Convert everything to m³/h as base unit, then to target
  let valueInM3h = numValue;

  // Convert from source unit to m³/h
  switch (fromUnit) {
    case 'gpm':
      valueInM3h = numValue / CONVERSION_FACTORS.FLOW.m3h_to_gpm;
      break;
    case 'gpd':
      valueInM3h = numValue / CONVERSION_FACTORS.FLOW.m3h_to_gpm / CONVERSION_FACTORS.FLOW.gpm_to_gpd * 24;
      break;
    case 'm³/d':
      valueInM3h = numValue / CONVERSION_FACTORS.FLOW.m3h_to_m3d;
      break;
    case 'm³/h':
      // Already in base unit
      break;
    default:
      console.warn(`Unknown flow unit: ${fromUnit}`);
      return value;
  }

  // Convert from m³/h to target unit
  switch (toUnit) {
    case 'gpm':
      return valueInM3h * CONVERSION_FACTORS.FLOW.m3h_to_gpm;
    case 'gpd':
      return valueInM3h * CONVERSION_FACTORS.FLOW.m3h_to_gpm * CONVERSION_FACTORS.FLOW.gpm_to_gpd;
    case 'm³/d':
      return valueInM3h * CONVERSION_FACTORS.FLOW.m3h_to_m3d;
    case 'm³/h':
      return valueInM3h;
    default:
      // console.warn(`Unknown flow unit: ${toUnit}`);
      return value;
  }
};

/**
 * Generic conversion function that routes to the appropriate converter
 * @param {number} value - The value to convert
 * @param {string} parameterType - Type of parameter ('pressure', 'flow', 'conductivity', 'temperature')
 * @param {string} fromSystem - Source unit system ('Metric', 'US', or 'Custom')
 * @param {string} toSystem - Target unit system ('Metric', 'US', or 'Custom')
 * @param {Object} fromCustomUnits - Custom units for source system (when fromSystem is 'Custom')
 * @param {Object} toCustomUnits - Custom units for target system (when toSystem is 'Custom')
 * @returns {number} Converted value
 */
export const convertValue = (value, parameterType, fromSystem, toSystem, fromCustomUnits = {}, toCustomUnits = {}) => {
  // Handle custom unit conversions
  if (fromSystem === UNIT_SYSTEMS.CUSTOM || toSystem === UNIT_SYSTEMS.CUSTOM) {
    const fromUnit = fromSystem === UNIT_SYSTEMS.CUSTOM
      ? fromCustomUnits[parameterType]
      : getUnitLabel(parameterType, fromSystem);
    const toUnit = toSystem === UNIT_SYSTEMS.CUSTOM
      ? toCustomUnits[parameterType]
      : getUnitLabel(parameterType, toSystem);

    return convertBetweenCustomUnits(value, parameterType, fromUnit, toUnit);
  }

  // Standard system conversions
  const converters = {
    pressure: convertPressure,
    flow: convertFlow,
    conductivity: convertConductivity,
    temperature: convertTemperature
  };

  const converter = converters[parameterType];
  if (!converter) {
    console.warn(`No converter found for parameter type: ${parameterType}`);
    return value;
  }

  return converter(value, fromSystem, toSystem);
};

/**
 * Get the unit label for a parameter type in a specific unit system
 * @param {string} parameterType - Type of parameter ('pressure', 'flow', 'conductivity', 'temperature')
 * @param {string} unitSystem - Unit system ('Metric', 'US', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (only used when unitSystem is 'Custom')
 * @returns {string} Unit label
 */
export const getUnitLabel = (parameterType, unitSystem, customUnits = {}) => {
  // Handle Custom unit system
  if (unitSystem === UNIT_SYSTEMS.CUSTOM) {
    // Use custom units if provided, otherwise fall back to defaults
    if (customUnits[parameterType]) {
      return customUnits[parameterType];
    }

    // Fall back to default custom units based on parameter type
    const customConfig = UNIT_LABELS[UNIT_SYSTEMS.CUSTOM][parameterType];
    if (customConfig) {
      if (parameterType === 'flow') {
        return customConfig.defaultMetric; // Default to metric for flow
      } else {
        return customConfig.metric || customConfig.us; // Default to metric, fallback to US
      }
    }

    // Final fallback to Metric system
    return UNIT_LABELS[UNIT_SYSTEMS.METRIC]?.[parameterType] || '';
  }

  // Handle standard unit systems (Metric, US)
  return UNIT_LABELS[unitSystem]?.[parameterType] || '';
};



/**
 * Round a converted value to an appropriate number of decimal places
 * @param {number} value - The value to round
 * @param {string} parameterType - Type of parameter for context-specific rounding
 * @returns {number} Rounded value
 */
export const roundConvertedValue = (value, parameterType) => {
  if (!value || isNaN(value)) return value;

  // Define precision based on parameter type and typical value ranges
  const precision = {
    pressure: 2,      // 2 decimal places for pressure
    flow: 1,          // 1 decimal place for flow
    conductivity: 0,  // Whole numbers for conductivity
    temperature: 1    // 1 decimal place for temperature
  };

  const decimals = precision[parameterType] || 2;
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

/**
 * Validate if a custom unit is among the allowed units for a given type
 * @param {string} type - The type of parameter ('flow', 'pressure', 'conductivity', 'temperature')
 * @param {string} unit - The unit to validate
 * @returns {boolean} True if the unit is valid for the given type, false otherwise
 */
export const validateCustomUnit = (type, unit) => {
  const validUnits = {
    flow: [...UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].flow.us,
    ...UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].flow.metric],
    pressure: [UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].pressure.us,
    UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].pressure.metric],
    conductivity: [UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].conductivity.us,
    UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].conductivity.metric],
    temperature: [UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].temperature.us,
    UNIT_LABELS[UNIT_SYSTEMS.CUSTOM].temperature.metric]
  };

  return validUnits[type]?.includes(unit) || false;
};

// Removed duplicate convertValue function with wrong signature

// Add a helper function to determine the unit type for a field
export const getUnitType = (fieldName) => {
  if (fieldName.toLowerCase().includes('temperature')) return 'temperature';
  if (fieldName.toLowerCase().includes('flow')) return 'flow';
  if (fieldName.toLowerCase().includes('pressure')) return 'pressure';
  if (fieldName.toLowerCase().includes('conductivity')) return 'conductivity';
  return null;
};

// Add a function to handle unit system changes
export const convertFormValues = (formData, fromSystem, toSystem, customUnits) => {
  const convertField = (value, fieldName) => {
    const type = getUnitType(fieldName);
    if (!type) return value;

    const fromUnit = fromSystem === 'Custom'
      ? customUnits[type]
      : UNIT_LABELS[fromSystem][type];

    const toUnit = toSystem === 'Custom'
      ? customUnits[type]
      : UNIT_LABELS[toSystem][type];

    return convertValue(value, fromUnit, toUnit, type);
  };

  return Object.entries(formData).reduce((acc, [section, data]) => {
    acc[section] = Object.entries(data).reduce((fields, [field, value]) => {
      fields[field] = typeof value === 'number' ? convertField(value, field) : value;
      return fields;
    }, {});
    return acc;
  }, {});
};

/**
 * Validate if a custom unit conversion is valid for the given type
 * @param {string} fromUnit - The source unit
 * @param {string} toUnit - The target unit
 * @param {string} type - The type of parameter ('temperature', 'pressure', 'flow', 'conductivity')
 * @returns {boolean} True if the conversion is valid, false otherwise
 */
export const validateCustomUnitConversion = (fromUnit, toUnit, type) => {
  const validConversions = {
    temperature: ['°C', '°F'],
    pressure: ['bar', 'psi'],
    flow: ['m³/h', 'm³/d', 'gpm', 'gpd'],
    conductivity: ['μS/cm', 'ppm', 'mg/L']
  };

  return validConversions[type]?.includes(fromUnit) &&
    validConversions[type]?.includes(toUnit);
};

/**
 * Convert validation limits between unit systems with support for custom units
 * @param {Object} limits - Validation limits object with min/max
 * @param {string} parameterType - Type of parameter ('pressure', 'flow', 'conductivity', 'temperature')
 * @param {string} fromSystem - Source unit system ('Metric', 'US', or 'Custom')
 * @param {string} toSystem - Target unit system ('Metric', 'US', or 'Custom')
 * @param {Object} fromCustomUnits - Custom units for source system (when fromSystem is 'Custom')
 * @param {Object} toCustomUnits - Custom units for target system (when toSystem is 'Custom')
 * @returns {Object} Converted validation limits
 */
export const convertValidationLimits = (limits, parameterType, fromSystem, toSystem, fromCustomUnits = {}, toCustomUnits = {}) => {
  if (!limits || fromSystem === toSystem) return limits;

  const result = { ...limits };

  // Convert min value if present
  if (limits.min !== undefined) {
    result.min = Math.round(convertValue(
      limits.min,
      parameterType,
      fromSystem,
      toSystem,
      fromCustomUnits,
      toCustomUnits
    ) * 100) / 100; // Round to 2 decimal places
  }

  // Convert max value if present
  if (limits.max !== undefined) {
    result.max = Math.round(convertValue(
      limits.max,
      parameterType,
      fromSystem,
      toSystem,
      fromCustomUnits,
      toCustomUnits
    ) * 100) / 100; // Round to 2 decimal places
  }

  // Update unit label
  result.unit = getUnitLabel(parameterType, toSystem, toCustomUnits);

  return result;
};

/**
 * Determine which base unit system a custom unit belongs to
 * @param {string} parameterType - Type of parameter
 * @param {string} customUnit - Custom unit
 * @returns {string} Base unit system ('Metric' or 'US')
 */
export const determineBaseUnitSystem = (parameterType, customUnit) => {
  const metricUnits = {
    pressure: ['bar'],
    flow: ['m³/h', 'm³/d'],
    conductivity: ['μS/cm', 'mg/L'],
    temperature: ['°C']
  };

  const usUnits = {
    pressure: ['psi'],
    flow: ['gpm', 'gpd'],
    conductivity: ['ppm'],
    temperature: ['°F']
  };

  // Check if the custom unit belongs to US system
  if (usUnits[parameterType]?.includes(customUnit)) {
    return UNIT_SYSTEMS.US;
  }

  // Check if the custom unit belongs to Metric system
  if (metricUnits[parameterType]?.includes(customUnit)) {
    return UNIT_SYSTEMS.METRIC;
  }

  // Default to Metric system if unit not found
  return UNIT_SYSTEMS.METRIC;
};
