import { PARAMETER_TYPES, UNITS_CONFIG } from '../constants';
import { UNIT_SYSTEMS, getUnitLabel, convertValidationLimits, determineBaseUnitSystem } from '../utils/unitConversion';

/**
 * Configuration for parameter rows with unit-specific validation limits
 */

/**
 * Get parameter configuration with dynamic labels based on unit system
 * @param {string} unitSystem - Current unit system ('US', 'Metric', 'Custom')
 * @returns {Array} Parameter configuration array
 */
export const getParameterConfigWithLabels = (unitSystem = UNIT_SYSTEMS.METRIC) => [
  {
    type: PARAMETER_TYPES.FEED_PRESSURE,
    label: 'Feed Pressure',
    unit: UNITS_CONFIG.pressure,
    parameterType: 'pressure', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        start: { min: 0, max: 100 },
        current: { min: 0, max: 100 },
        limit: { min: 0, max: 150 },
      },
      [UNIT_SYSTEMS.US]: {
        start: { min: 0, max: 1450 }, // ~100 bar * 14.5038
        current: { min: 0, max: 1450 },
        limit: { min: 0, max: 2176 }, // ~150 bar * 14.5038
      }
    }
  },
  {
    type: PARAMETER_TYPES.DP,
    label: 'dP',
    unit: UNITS_CONFIG.pressure,
    parameterType: 'pressure', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        start: { min: 0, max: 50 },
        current: { min: 0, max: 50 },
        limit: { min: 0, max: 100 },
      },
      [UNIT_SYSTEMS.US]: {
        start: { min: 0, max: 725 }, // ~50 bar * 14.5038
        current: { min: 0, max: 725 },
        limit: { min: 0, max: 1450 }, // ~100 bar * 14.5038
      }
    }
  },
  {
    type: PARAMETER_TYPES.Q_PERM,
    label: 'Q perm',
    unit: UNITS_CONFIG.feedFlow,
    parameterType: 'flow', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        start: { min: 0, max: 12000 }, // 500 m³/h * 24 = 12000 m³/d
        current: { min: 0, max: 12000 },
        limit: { min: 0, max: 2400 }, // 100 m³/h * 24 = 2400 m³/d
      },
      [UNIT_SYSTEMS.US]: {
        start: { min: 0, max: 3170400 }, // ~500 m³/h * 24 * 264.172 = 3,170,400 gpd
        current: { min: 0, max: 3170400 },
        limit: { min: 0, max: 634080 }, // ~100 m³/h * 24 * 264.172 = 634,080 gpd
      }
    }
  },
  {
    type: PARAMETER_TYPES.COND_PERM,
    label: unitSystem === 'US' ? 'Cond perm' : 'TDS perm',
    unit: UNITS_CONFIG.feedConductivity,
    parameterType: 'conductivity', // For unit conversion
    validation: {
      [UNIT_SYSTEMS.METRIC]: {
        start: { min: 0, max: 200 },
        current: { min: 0, max: 200 },
        limit: { min: 0, max: 500 },
      },
      [UNIT_SYSTEMS.US]: {
        start: { min: 0, max: 100 }, // ~200 μS/cm * 0.5
        current: { min: 0, max: 100 },
        limit: { min: 0, max: 250 }, // ~500 μS/cm * 0.5
      }
    }
  }
];

/**
 * Default parameter configuration (uses 'Cond perm' label)
 */
export const PARAMETER_CONFIG = getParameterConfigWithLabels(UNIT_SYSTEMS.METRIC);

/**
 * Get parameter configuration by type
 * @param {string} parameterType - The parameter type
 * @param {string} unitSystem - Optional unit system for dynamic labels
 * @returns {Object|null} Parameter configuration or null if not found
 */
export const getParameterConfig = (parameterType, unitSystem = UNIT_SYSTEMS.METRIC) => {
  const config = getParameterConfigWithLabels(unitSystem);
  return config.find(item => item.type === parameterType) || null;
};

/**
 * Get parameter configuration with validation limits for a specific unit system
 * @param {string} parameterType - The parameter type
 * @param {string} unitSystem - The unit system ('Metric', 'US', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {Object|null} Parameter configuration with unit-specific validation or null if not found
 */
export const getParameterConfigForUnit = (parameterType, unitSystem, customUnits = {}) => {
  const config = getParameterConfig(parameterType);
  if (!config) return null;

  // For Custom unit system, determine the base validation system
  let validationSystem = unitSystem;
  if (unitSystem === UNIT_SYSTEMS.CUSTOM) {
    const customUnit = customUnits[config.parameterType];
    if (customUnit) {
      // Determine which base system the custom unit belongs to
      const metricUnits = {
        pressure: ['bar'],
        flow: ['m³/h', 'm³/d'],
        conductivity: ['mg/L'],
        temperature: ['°C']
      };

      const usUnits = {
        pressure: ['psi'],
        flow: ['gpm', 'gpd'],
        conductivity: ['ppm', 'μS/cm'],
        temperature: ['°F']
      };

      // Determine base system for the custom unit
      if (usUnits[config.parameterType]?.includes(customUnit)) {
        validationSystem = UNIT_SYSTEMS.US;
      } else if (metricUnits[config.parameterType]?.includes(customUnit)) {
        validationSystem = UNIT_SYSTEMS.METRIC;
      } else {
        validationSystem = UNIT_SYSTEMS.METRIC; // Default fallback
      }
    } else {
      validationSystem = UNIT_SYSTEMS.METRIC; // Default fallback
    }
  }

  return {
    ...config,
    validation: config.validation[validationSystem] || config.validation[UNIT_SYSTEMS.METRIC]
  };
};

/**
 * Get validation limits for a parameter type in a specific unit system
 * @param {string} parameterType - The parameter type
 * @param {string} unitSystem - The unit system ('Metric', 'US', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {Object|null} Validation limits or null if not found
 */
export const getValidationLimits = (parameterType, unitSystem, customUnits = {}) => {
  const config = getParameterConfig(parameterType);
  if (!config) return null;

  // Handle Custom unit system
  if (unitSystem === UNIT_SYSTEMS.CUSTOM) {
    const customUnit = customUnits[parameterType];
    if (customUnit) {
      // Determine which base system the custom unit belongs to
      const metricUnits = {
        pressure: ['bar'],
        flow: ['m³/h', 'm³/d'],
        conductivity: ['mg/L'],
        temperature: ['°C']
      };

      const usUnits = {
        pressure: ['psi'],
        flow: ['gpm', 'gpd'],
        conductivity: ['ppm', 'μS/cm'],
        temperature: ['°F']
      };

      // Determine base system for the custom unit
      let baseSystem = UNIT_SYSTEMS.METRIC; // Default
      if (usUnits[parameterType]?.includes(customUnit)) {
        baseSystem = UNIT_SYSTEMS.US;
      } else if (metricUnits[parameterType]?.includes(customUnit)) {
        baseSystem = UNIT_SYSTEMS.METRIC;
      }

      return config.validation[baseSystem] || config.validation[UNIT_SYSTEMS.METRIC];
    }
  }

  return config.validation[unitSystem] || config.validation[UNIT_SYSTEMS.METRIC];
};

/**
 * Get validation limits for a specific parameter field in a specific unit system
 * with dynamic conversion for custom units
 *
 * @param {string} parameterType - The parameter type
 * @param {string} fieldName - The field name (start, current, limit)
 * @param {string} unitSystem - The unit system ('Metric', 'US', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {Object|null} Validation limits with min, max, and unit
 */
export const getParameterFieldValidation = (parameterType, fieldName, unitSystem, customUnits = {}) => {
  const config = getParameterConfig(parameterType);
  if (!config) return null;

  // Get the parameter conversion type (pressure, flow, etc.)
  const conversionType = config.parameterType;

  // For standard unit systems, use predefined validation
  if (unitSystem === UNIT_SYSTEMS.METRIC || unitSystem === UNIT_SYSTEMS.US) {
    const validation = config.validation[unitSystem]?.[fieldName];
    if (!validation) return null;

    return {
      ...validation,
      unit: getUnitLabel(conversionType, unitSystem)
    };
  }

  // For custom unit system, we need to dynamically convert validation limits
  if (unitSystem === UNIT_SYSTEMS.CUSTOM) {
    const customUnit = customUnits[conversionType];
    if (!customUnit) return null;

    // Determine base system for the custom unit
    const baseSystem = determineBaseUnitSystem(conversionType, customUnit);

    // Get base validation limits
    const baseValidation = config.validation[baseSystem]?.[fieldName];
    if (!baseValidation) return null;

    // Convert validation limits from base system to custom unit
    return convertValidationLimits(
      baseValidation,
      conversionType,
      baseSystem,
      UNIT_SYSTEMS.CUSTOM,
      {}, // No custom units for base system
      { [conversionType]: customUnit } // Custom unit for target system
    );
  }

  return null;
};

/**
 * Get all parameter types
 * @returns {Array} Array of parameter types
 */
export const getAllParameterTypes = () => {
  return PARAMETER_CONFIG.map(config => config.type);
};

/**
 * Get parameter configuration with labels appropriate for the current unit system
 * This function should be used in components that have access to the form context
 * @param {string} unitSystem - Current unit system from form context
 * @returns {Array} Parameter configuration with appropriate labels
 */
export const getParameterConfigForUnitSystem = (unitSystem) => {
  return getParameterConfigWithLabels(unitSystem);
};
