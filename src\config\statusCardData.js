import { getStatusCardIcon } from '../utils/iconUtils';
import { STATUS_TYPES } from '../constants';

/**
 * Configuration for status cards
 */
export const STATUS_CARD_CONFIG = [
  {
    id: 'feedPressure',
    title: 'Feed Pressure',
    value: '70 bar',
    status: STATUS_TYPES.NORMAL,
    estimatedLife: true,
    date: 'Nov 24',
    months: '8',
    iconSrc: getStatusCardIcon('feedPressure')
  },
  {
    id: 'dP',
    title: 'dP',
    value: '0.60 bar',
    status: STATUS_TYPES.WARNING,
    estimatedLife: true,
    date: 'Apr 26',
    months: '8',
    iconSrc: getStatusCardIcon('dP')
  },
  {
    id: 'qPerm',
    title: 'Q Perm',
    value: '90 m³/h',
    status: STATUS_TYPES.NORMAL,
    estimatedLife: true,
    date: 'Nov 25',
    months: '8',
    iconSrc: getStatusCardIcon('qPerm')
  },
  {
    id: 'condPerm',
    title: 'Cond Perm',
    value: '180 μS/cm',
    status: STATUS_TYPES.NORMAL,
    estimatedLife: true,
    date: 'Nov 24',
    months: '8',
    iconSrc: getStatusCardIcon('condPerm')
  }
];
