/**
 * Centralized validation system exports
 * Main entry point for all validation utilities
 */

// Core validation utilities
export {
  VALIDATION_ERRORS,
  DEFAULT_ERROR_MESSAGES,
  isEmpty,
  isValidNumber,
  toNumber,
  validateRequired,
  validateNumber,
  validateRange,
  validateDate,
  validateEmail,
  validateWithRules,
  createValidationRule
} from './validationCore';

// Parameter validation utilities
export {
  EXCLUDED_PARAMETER_FIELDS,
  READ_ONLY_PARAMETER_FIELDS,
  shouldValidateParameterField,
  shouldShowParameterError,
  getParameterFieldValidation,
  createParameterFieldRules,
  validateParameterField,
  validateParameterFields,
  areParameterFieldsValid,
  getParameterValidationErrors,
  validateParameterValue,
  createParameterErrorMessage,
  sanitizeParameterInput
} from './parameterValidation';

// Form validation utilities
export {
  FORM_FIELD_TYPES,
  VALIDATION_PRESETS,
  validateFormField,
  validateNumericInput,
  validateDateInput,
  validateCIPFrequency,
  validateCIPDuration,
  validateTemperature,
  validateFlowRate,
  validateConductivity,
  validateFormData,
  isFormValid,
  getFormValidationErrors,
  getNestedValue,
  setNestedValue,
  createFormValidationSchema,
  COMMON_VALIDATION_SCHEMAS
} from './formValidation';

// Validation hooks
export {
  useParameterValidation,
  useFormValidation,
  useNumericValidation,
  useDateValidation,
  useCustomValidation
} from './useValidation';

/**
 * Quick validation utilities for common use cases
 */

/**
 * Validate a simple required field
 * @param {any} value - Value to validate
 * @param {string} fieldName - Field name for error message
 * @returns {ValidationResult} Validation result
 */
export const validateRequiredField = (value, fieldName = 'Field') => {
  return validateRequired(value, `${fieldName} is required`);
};

/**
 * Validate a simple numeric field
 * @param {any} value - Value to validate
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @param {string} unit - Unit for error message
 * @returns {ValidationResult} Validation result
 */
export const validateNumericField = (value, min, max, unit = '') => {
  const numberResult = validateNumber(value);
  if (!numberResult.isValid) {
    return numberResult;
  }
  
  return validateRange(value, min, max, unit);
};

/**
 * Validate a simple date field
 * @param {string} value - Date string to validate
 * @param {boolean} required - Whether field is required
 * @returns {ValidationResult} Validation result
 */
export const validateDateField = (value, required = false) => {
  if (required) {
    const requiredResult = validateRequired(value);
    if (!requiredResult.isValid) {
      return requiredResult;
    }
  }
  
  return validateDate(value);
};

/**
 * Create a simple validation schema
 * @param {Object} fields - Field definitions
 * @returns {Object} Validation schema
 */
export const createSimpleValidationSchema = (fields) => {
  const schema = {};
  
  Object.entries(fields).forEach(([fieldName, config]) => {
    if (typeof config === 'string') {
      // Simple type definition
      schema[fieldName] = {
        type: config,
        required: false
      };
    } else {
      // Full configuration
      schema[fieldName] = {
        type: config.type || 'text',
        required: config.required || false,
        rules: config.rules || [],
        label: config.label || fieldName
      };
    }
  });
  
  return schema;
};

/**
 * Validation utilities for specific components
 */
export const ComponentValidators = {
  /**
   * Parameter row validation
   */
  ParameterRow: {
    validateField: validateParameterField,
    shouldValidate: shouldValidateParameterField,
    shouldShowError: shouldShowParameterError
  },
  
  /**
   * Numeric input validation
   */
  NumericInput: {
    validate: validateNumericInput,
    validateRange: (value, min, max, unit) => validateRange(value, min, max, unit)
  },
  
  /**
   * Date input validation
   */
  DateInput: {
    validate: validateDateInput,
    validateRequired: (value) => validateDateField(value, true)
  },
  
  /**
   * CIP section validation
   */
  CIPSection: {
    validateFrequency: validateCIPFrequency,
    validateDuration: validateCIPDuration
  }
};

/**
 * Validation error helpers
 */
export const ValidationHelpers = {
  /**
   * Check if validation result has error
   * @param {ValidationResult} result - Validation result
   * @returns {boolean} True if has error
   */
  hasError: (result) => !result.isValid && !!result.error,
  
  /**
   * Get error message from validation result
   * @param {ValidationResult} result - Validation result
   * @returns {string} Error message
   */
  getErrorMessage: (result) => result.error || '',
  
  /**
   * Check if multiple validation results are all valid
   * @param {ValidationResult[]} results - Array of validation results
   * @returns {boolean} True if all valid
   */
  allValid: (results) => results.every(result => result.isValid),
  
  /**
   * Get all error messages from multiple validation results
   * @param {ValidationResult[]} results - Array of validation results
   * @returns {string[]} Array of error messages
   */
  getAllErrors: (results) => results
    .filter(result => !result.isValid && result.error)
    .map(result => result.error),
  
  /**
   * Combine multiple validation results
   * @param {ValidationResult[]} results - Array of validation results
   * @returns {ValidationResult} Combined result
   */
  combineResults: (results) => {
    const isValid = results.every(result => result.isValid);
    const errors = results
      .filter(result => !result.isValid && result.error)
      .map(result => result.error);
    
    return {
      isValid,
      error: errors.length > 0 ? errors[0] : '', // Return first error
      errors: errors, // All errors
      value: results.length > 0 ? results[0].value : undefined
    };
  }
};

/**
 * Default export with commonly used validators
 */
export default {
  // Core validators
  validateRequired,
  validateNumber,
  validateRange,
  validateDate,
  
  // Quick validators
  validateRequiredField,
  validateNumericField,
  validateDateField,
  
  // Hooks
  useParameterValidation,
  useFormValidation,
  useNumericValidation,
  useDateValidation,
  
  // Helpers
  ValidationHelpers,
  ComponentValidators
};
