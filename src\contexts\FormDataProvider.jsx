/**
 * Form Data Provider
 * Manages centralized form state including form data, parameters, and CIP data
 */

import { useState, useMemo, useCallback, useEffect } from 'react';
import { FormDataContext } from './FormContext.jsx';
import { getUnitLabel } from '../utils/unitConversion';
import { FORM_SECTIONS, PARAMETER_TYPES } from '../constants';
import { getCurrentDate, getDateThreeYearsAgo, getDateThreeYearsFromNow } from '../utils/dateUtils';


// Initial form data - moved from useFormData hook
const INITIAL_FORM_DATA = {
  [FORM_SECTIONS.START]: {
    date: getDateThreeYearsAgo(), // Current date - 3 years
    temperature: '25',
    feedFlow: '1000',
    feedConductivity: '1200'
  },
  [FORM_SECTIONS.CURRENT]: {
    date: getCurrentDate(),
    temperature: '20',
    feedFlow: '1000',
    feedConductivity: '1500'
  },
  [FORM_SECTIONS.FORECAST]: {
    date: getDateThreeYearsFromNow(), // Current date + 3 years
    temperature: '30',
    feedFlow: '1200',
    feedConductivity: '2000'
  }
};

// Initial parameters data - moved from useParameters hook
const INITIAL_PARAMETERS = {
  [PARAMETER_TYPES.FEED_PRESSURE]: {
    start: '20',
    current: '70',
    normalized: '60',
    limit: '95',
    threshold: ''
  },
  [PARAMETER_TYPES.DP]: {
    start: '0.50',
    current: '0.60',
    normalized: '0.57',
    limit: '2.00',
    threshold: '3%'
  },
  [PARAMETER_TYPES.Q_PERM]: {
    start: '100',
    current: '90',
    normalized: '105',
    limit: '20',
    threshold: '10%'
  },
  [PARAMETER_TYPES.COND_PERM]: {
    start: '200',
    current: '180',
    normalized: '209',
    limit: '1000',
    threshold: '10%'
  }
};

// Initial CIP data
const INITIAL_CIP_DATA = {
  frequency: 15,
  duration: '00'
};

/**
 * Form Data Provider Component
 * Provides centralized form state management through React Context
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.initialUnitSystem='US'] - Initial unit system
 * @param {Object} [props.initialFormData] - Override initial form data
 * @param {Object} [props.initialParameters] - Override initial parameters
 * @param {Object} [props.initialCipData] - Override initial CIP data
 * @param {Object} [props.customUnits] - Custom unit preferences
 * @param {Function} [props.onCustomUnitsChange] - Callback when custom units change
 */
export function FormDataProvider({
  children,
  initialUnitSystem = 'Metric',
  initialFormData = INITIAL_FORM_DATA,
  initialParameters = INITIAL_PARAMETERS,
  initialCipData = INITIAL_CIP_DATA,
  customUnits = {},
  onCustomUnitsChange
}) {
  // Load saved unit system from localStorage
  const loadSavedUnitSystem = () => {
    try {
      const saved = localStorage.getItem('ro-advisor-unit-system');
      return saved || initialUnitSystem;
    } catch (error) {
      console.error('Error loading unit system:', error);
      return initialUnitSystem;
    }
  };

  // Core state management
  const [formData, setFormData] = useState(initialFormData);
  const [parameters, setParameters] = useState(initialParameters);
  const [cipData, setCipData] = useState(initialCipData);
  const [unitSystem, setUnitSystem] = useState(loadSavedUnitSystem);

  // Save unit system to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('ro-advisor-unit-system', unitSystem);
    } catch (error) {
      console.error('Error saving unit system:', error);
    }
  }, [unitSystem]);

  // Handle custom units changes - trigger callback if provided
  useEffect(() => {
    if (onCustomUnitsChange && typeof onCustomUnitsChange === 'function') {
      // Only call if custom units have actually changed
      // This effect will run when customUnits prop changes from parent
    }
  }, [customUnits, onCustomUnitsChange]);

  // Unit labels - computed using the unit conversion system with custom units support
  const unitLabels = useMemo(() => {
    return {
      temperature: getUnitLabel('temperature', unitSystem, customUnits),
      flow: getUnitLabel('flow', unitSystem, customUnits),
      conductivity: getUnitLabel('conductivity', unitSystem, customUnits),
      pressure: getUnitLabel('pressure', unitSystem, customUnits)
    };
  }, [unitSystem, customUnits]);

  // Create callback functions outside of useMemo to follow Rules of Hooks
  const getFormSection = useCallback((section) => formData[section], [formData]);
  const getParameter = useCallback((parameterType) => parameters[parameterType], [parameters]);

  const getFormFieldValue = useCallback((section, field) => {
    return formData[section]?.[field] || '';
  }, [formData]);

  const getParameterFieldValue = useCallback((parameterType, field) => {
    return parameters[parameterType]?.[field] || '';
  }, [parameters]);

  const getCipFieldValue = useCallback((field) => {
    return cipData[field];
  }, [cipData]);

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    // Core state
    formData,
    parameters,
    cipData,
    unitSystem,
    unitLabels,
    customUnits,

    // State setters (for internal use and unit conversion)
    setFormData,
    setParameters,
    setCipData,
    setUnitSystem,

    // Custom units management
    onCustomUnitsChange,

    // Computed properties
    isFormEmpty: Object.values(formData).every(section =>
      Object.values(section).every(value => !value || (typeof value === 'string' && value.trim() === ''))
    ),

    isParametersEmpty: Object.values(parameters).every(param =>
      Object.values(param).every(value => !value || (typeof value === 'string' && value.trim() === ''))
    ),

    // Form data getters
    getFormSection,
    getParameter,

    // Validation helpers
    getFormFieldValue,
    getParameterFieldValue,
    getCipFieldValue

  }), [formData, parameters, cipData, unitSystem, unitLabels, customUnits, onCustomUnitsChange, getFormSection, getParameter, getFormFieldValue, getParameterFieldValue, getCipFieldValue]);

  return (
    <FormDataContext.Provider value={contextValue}>
      {children}
    </FormDataContext.Provider>
  );
}

/**
 * Default export for convenience
 */
export default FormDataProvider;
