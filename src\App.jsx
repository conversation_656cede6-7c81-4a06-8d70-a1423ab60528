import { useState, useCallback, useEffect } from 'react';
import './App.css';

// Form Context System
import { FormProvider, useFormData, useFormActions } from './contexts';

// Configuration
import { FORM_FIELD_TYPES } from './config/formFieldConfig';

// Components
import DateInput from './components/DateInput/DateInput';
import NumericInput from './components/NumericInput';
import ButtonGroup from './components/ButtonGroup';
import StatusCard from './components/StatusCard';
import ParameterTable from './components/ParameterTable/ParameterTable';
import CustomUnitModal from './components/CustomUnitModal';
import NumericInputWorkingExample from './components/NumericInputWorkingExample';
import { getDateValidationRules } from './utils/validation/dateValidation';

import { Row, Col, InputNumber, Form } from 'antd';

// Hooks
import { useCurrentConditions } from './hooks/useCurrentConditions';
import { useCustomUnits } from './hooks/useCustomUnits';

// Constants and Configuration
import {
  MEMBRANE_OPTIONS,
  UNIT_OPTIONS,
  FORM_SECTIONS,
} from './constants/index.js';

/**
 * App Content Component
 * Contains the main application logic using form context hooks
 */
function AppContent({
  customUnits,
  isCustomUnitsModalVisible,
  handleCustomUnitsSave,
  showCustomUnitsModal,
  handleCustomUnitsCancel,
}) {
  // Settings state (non-form related)
  const [membraneType, setMembraneType] = useState('RO');
  const [showOutput, setShowOutput] = useState(false);

  // Form instance for handling converted values
  const [form] = Form.useForm();

  // Form context hooks
  const { formData, parameters, cipData, unitSystem, unitLabels } =
    useFormData();
  const {
    updateFormField,
    updateCIPFrequency,
    updateCIPDuration,
    changeUnitSystem,
    setFormData,
    setParameters,
  } = useFormActions();

  // Handle custom units save with unit system refresh
  const handleCustomUnitsSaveWithRefresh = newCustomUnits => {
    // If we're currently in Custom unit system, convert values from old to new custom units
    if (unitSystem === 'Custom') {
      changeUnitSystem('Custom', {
        formData,
        parameters,
        customUnits: newCustomUnits,
        onFormDataConverted: convertedFormData => {
          setFormData(convertedFormData);
          // Update the form instance with converted values
          form.setFieldsValue(convertedFormData);
        },
        onParametersConverted: setParameters,
      });
    }

    // Save the custom units after conversion
    handleCustomUnitsSave(newCustomUnits);
  };

  // Non-form hooks
  const { isLoading, conditions, recalculate } = useCurrentConditions();

  // Update form fields when formData changes (e.g., after unit conversion)
  useEffect(() => {
    form.setFieldsValue(formData);
  }, [form, formData]);

  // Handle unit system changes with context integration
  const handleUnitChange = useCallback(
    newUnitSystem => {
      // Always update the unit system, regardless of type
      changeUnitSystem(newUnitSystem, {
        formData,
        parameters,
        customUnits: customUnits, // Pass current custom units for conversion
        onFormDataConverted: convertedFormData => {
          setFormData(convertedFormData);
          // Update the form instance with converted values
          form.setFieldsValue(convertedFormData);
          form.validateFields();
        },
        onParametersConverted: setParameters,
      });

      // Note: Custom units modal is now opened only via the edit icon click
      // in the ButtonGroup component, not when selecting the Custom option
    },
    [
      changeUnitSystem,
      formData,
      parameters,
      customUnits,
      setFormData,
      setParameters,
      form,
    ]
  );

  const conductivityLabel = unitSystem === 'US' ? 'Feed Conductivity' : 'TDS';

  const getStartDateValidationRules = () => {
    const startDate = formData.start.date;
    const currentDate = formData.current.date;
    const forecastDate = formData.forecast.date;

    return getDateValidationRules({
      startDate,
      currentDate,
      forecastDate,
      isStartDate: true,
      isCurrentDate: false,
      isForecastDate: false,
    });
  };

  const getCurrentDateValidationRules = () => {
    const startDate = formData.start.date;
    const currentDate = formData.current.date;
    const forecastDate = formData.forecast.date;

    return getDateValidationRules({
      startDate,
      currentDate,
      forecastDate,
      isStartDate: false,
      isCurrentDate: true,
      isForecastDate: false,
    });
  };

  const getForecastDateValidationRules = () => {
    const startDate = formData.start.date;
    const currentDate = formData.current.date;
    const forecastDate = formData.forecast.date;

    return getDateValidationRules({
      startDate,
      currentDate,
      forecastDate,
      isStartDate: false,
      isCurrentDate: false,
      isForecastDate: true,
    });
  };

  return (
    <div className="ro-advisor">
      <header className="header">
        <h1 className="title">RO Advisor lite</h1>
        <p className="description">
          Some sort of instructions to tell what is this lite version and how
          should be using this. Some sort of instructions to tell what is this
          lite version and how should be using this.
        </p>
      </header>

      <div className="settings-section">
        <Row gutter={[32, 16]} className="settings-row">
          <Col xs={24} sm={12} lg={8}>
            <div className="setting-group">
              <h2 className="setting-title">
                <span className="required">*</span>Membrane Type
              </h2>
              <ButtonGroup
                options={MEMBRANE_OPTIONS}
                selectedOption={membraneType}
                onSelect={setMembraneType}
              />
            </div>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <div className="setting-group">
              <h2 className="setting-title">
                <span className="required">*</span>Units
              </h2>
              <div className="units-container">
                <ButtonGroup
                  options={UNIT_OPTIONS}
                  selectedOption={unitSystem}
                  onSelect={handleUnitChange}
                  showCustomUnitsModal={showCustomUnitsModal} // Pass the modal handler
                />
              </div>
            </div>
          </Col>

          {/* CIP Settings */}
          <Col xs={24} sm={12} lg={4}>
            <div className="setting-group">
              <h2 className="setting-title">CIP Frequency/Year</h2>
              <div className="cip-input-wrapper">
                <button
                  className="cip-decrement-btn"
                  onClick={() =>
                    updateCIPFrequency((cipData.frequency || 0) - 1)
                  }
                >
                  -
                </button>
                <InputNumber
                  value={cipData.frequency}
                  onChange={updateCIPFrequency}
                  className="cip-number-input"
                  controls={false}
                  min={0}
                />
                <button
                  className="cip-increment-btn"
                  onClick={() =>
                    updateCIPFrequency((cipData.frequency || 0) + 1)
                  }
                >
                  +
                </button>
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <div className="setting-group">
              <h2 className="setting-title">CIP Duration</h2>
              <div className="cip-input-wrapper">
                <InputNumber
                  value={cipData.duration}
                  onChange={updateCIPDuration}
                  className="cip-number-input"
                  controls={false}
                  min={0}
                />
                <span className="cip-unit-label">days</span>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      <main className="main-content">
        <Form
          form={form}
          layout="vertical"
          validateTrigger={['onChange', 'onBlur']}
          validateFirst
          initialValues={formData}
          onValuesChange={changedValues => {
            // Update form context when values change
            Object.keys(changedValues).forEach(section => {
              if (changedValues[section]) {
                Object.keys(changedValues[section]).forEach(field => {
                  updateFormField(
                    section,
                    field,
                    changedValues[section][field]
                  );
                });
              }
            });
          }}
        >
          <Row gutter={[24, 16]} className="data-input-grid">
            <Col xs={24} lg={8}>
              <div className="data-column data-column-start">
                <h3 className="column-header">Start</h3>
                <div className="input-row">
                  <DateInput
                    label="Date"
                    name={['start', 'date']}
                    rules={getStartDateValidationRules()}
                    onChange={value =>
                      updateFormField(FORM_SECTIONS.START, 'date', value)
                    }
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['start', 'temperature']}
                    label="Temperature"
                    formFieldType={FORM_FIELD_TYPES.TEMPERATURE}
                    required={true}
                    addonAfter={unitLabels.temperature}
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['start', 'feedFlow']}
                    label="Feed Flow"
                    formFieldType={FORM_FIELD_TYPES.FEED_FLOW}
                    required={true}
                    addonAfter={unitLabels.flow}
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['start', 'feedConductivity']}
                    label={conductivityLabel}
                    formFieldType={FORM_FIELD_TYPES.FEED_CONDUCTIVITY}
                    required={true}
                    addonAfter={unitLabels.conductivity}
                  />
                </div>
              </div>
            </Col>

            <Col xs={24} lg={8}>
              <div className="data-column data-column-current">
                <h3 className="column-header">Current</h3>
                <div className="input-row">
                  <DateInput
                    label="Date"
                    name={['current', 'date']}
                    rules={getCurrentDateValidationRules()}
                    onChange={value =>
                      updateFormField(FORM_SECTIONS.CURRENT, 'date', value)
                    }
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['current', 'temperature']}
                    label="Temperature"
                    formFieldType={FORM_FIELD_TYPES.TEMPERATURE}
                    required={true}
                    addonAfter={unitLabels.temperature}
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['current', 'feedFlow']}
                    label="Feed Flow"
                    formFieldType={FORM_FIELD_TYPES.FEED_FLOW}
                    required={true}
                    addonAfter={unitLabels.flow}
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['current', 'feedConductivity']}
                    label={conductivityLabel}
                    formFieldType={FORM_FIELD_TYPES.FEED_CONDUCTIVITY}
                    required={true}
                    addonAfter={unitLabels.conductivity}
                  />
                </div>
              </div>
            </Col>

            <Col xs={24} lg={8}>
              <div className="data-column data-column-forecast">
                <h3 className="column-header">Forecast Conditions</h3>
                <div className="input-row">
                  <DateInput
                    label="Date"
                    name={['forecast', 'date']}
                    rules={getForecastDateValidationRules()}
                    onChange={value =>
                      updateFormField(FORM_SECTIONS.FORECAST, 'date', value)
                    }
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['forecast', 'temperature']}
                    label="Temperature"
                    formFieldType={FORM_FIELD_TYPES.TEMPERATURE}
                    required={true}
                    addonAfter={unitLabels.temperature}
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['forecast', 'feedFlow']}
                    label="Feed Flow"
                    formFieldType={FORM_FIELD_TYPES.FEED_FLOW}
                    required={true}
                    addonAfter={unitLabels.flow}
                  />
                </div>
                <div className="input-row">
                  <NumericInput
                    name={['forecast', 'feedConductivity']}
                    label={conductivityLabel}
                    formFieldType={FORM_FIELD_TYPES.FEED_CONDUCTIVITY}
                    required={true}
                    addonAfter={unitLabels.conductivity}
                  />
                </div>
              </div>
            </Col>
          </Row>

          <ParameterTable />

          <div className="calculate-section">
            <button
              className="calculate-button"
              onClick={() => {
                recalculate();
                setShowOutput(true);
              }}
              disabled={isLoading}
            >
              Calculate Output Conditions
            </button>
          </div>
        </Form>

        {showOutput && (
          <div className="current-condition">
            <h3>
              {isLoading
                ? 'Calculating current conditions...'
                : 'Current Condition'}
            </h3>
            <Row gutter={[16, 16]} className="status-cards-row">
              {isLoading ? (
                // Loading state - show 4 loading cards
                <>
                  <Col xs={24} sm={12} md={12} lg={12}>
                    <StatusCard title="Feed Pressure" isLoading={true} />
                  </Col>
                  <Col xs={24} sm={12} md={12} lg={12}>
                    <StatusCard title="dP" isLoading={true} />
                  </Col>
                  <Col xs={24} sm={12} md={12} lg={12}>
                    <StatusCard title="Q Perm" isLoading={true} />
                  </Col>
                  <Col xs={24} sm={12} md={12} lg={12}>
                    <StatusCard title="Cond Perm" isLoading={true} />
                  </Col>
                </>
              ) : (
                // Loaded state - show actual data
                conditions.map(condition => (
                  <Col xs={24} sm={12} md={12} lg={12} key={condition.id}>
                    <StatusCard
                      title={condition.title}
                      status={condition.status}
                      estimatedLife={condition.estimatedLife}
                      date={condition.date}
                      months={condition.months}
                      iconSrc={condition.iconSrc}
                      isLoading={false}
                    />
                  </Col>
                ))
              )}
            </Row>
          </div>
        )}
      </main>

      <footer className="footer">
        <div className="disclaimer">
          <span className="disclaimer-icon">ℹ</span>
          <span className="disclaimer-text">
            Values are only estimates. DuPont does not provide warranties based
            on these numbers. Please consult TS&D
          </span>
        </div>
      </footer>

      {/* Custom Unit Modal */}
      <CustomUnitModal
        visible={isCustomUnitsModalVisible}
        onCancel={handleCustomUnitsCancel}
        onSave={handleCustomUnitsSaveWithRefresh}
        initialUnits={customUnits}
      />
    </div>
  );
}

/**
 * Main App Component
 * Wraps the application with FormProvider for centralized state management
 */
function App() {
  // Custom units state needs to be at this level to pass to FormProvider
  const {
    customUnits,
    isCustomUnitsModalVisible,
    handleCustomUnitsSave,
    showCustomUnitsModal,
    handleCustomUnitsCancel,
  } = useCustomUnits();

  return (
    <FormProvider
      initialUnitSystem="Metric"
      customUnits={customUnits}
      onCustomUnitsChange={handleCustomUnitsSave}
    >
      <AppContent
        customUnits={customUnits}
        isCustomUnitsModalVisible={isCustomUnitsModalVisible}
        handleCustomUnitsSave={handleCustomUnitsSave}
        showCustomUnitsModal={showCustomUnitsModal}
        handleCustomUnitsCancel={handleCustomUnitsCancel}
      />
    </FormProvider>
  );
}

export default App;
