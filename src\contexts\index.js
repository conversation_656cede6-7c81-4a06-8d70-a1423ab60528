/**
 * Form Context System - Main Export
 * Centralized form state management using React Context API
 */

// Core contexts
export {
  FormDataContext,
  FormActionsContext,
  FormValidationContext,
  FormFieldPath,
  CONTEXT_ERROR_MESSAGES
} from './FormContext.jsx';

// Provider components
export {
  FormProvider,
  MemoizedFormProvider,
  FormDataProvider,
  FormActionsProvider,
  FormValidationProvider
} from './FormProvider';

// Custom hooks
export {
  useFormData,
  useFormActions,
  useFormValidation,
  useFormContext,
  useSafeFormData,
  useSafeFormActions,
  useSafeFormValidation
} from './useFormContext';

/**
 * Default export - the main FormProvider for most use cases
 */
export { default } from './FormProvider';
