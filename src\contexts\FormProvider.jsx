/**
 * Form Provider
 * Main provider component that combines all form contexts with performance optimization
 */

import { memo } from 'react';
import FormDataProvider from './FormDataProvider';
import FormA<PERSON>Provider from './FormActionsProvider';
import FormValidationProvider from './FormValidationProvider';

/**
 * Form Provider Component
 * Combines all form contexts (data, actions, validation) with proper nesting
 * and performance optimization through context splitting
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.initialUnitSystem='US'] - Initial unit system
 * @param {Object} [props.initialFormData] - Override initial form data
 * @param {Object} [props.initialParameters] - Override initial parameters
 * @param {Object} [props.initialCipData] - Override initial CIP data
 * @param {Object} [props.customUnits] - Custom unit preferences
 * @param {Function} [props.onCustomUnitsChange] - Callback when custom units change
 * @param {boolean} [props.enableValidation=true] - Enable validation context
 * @param {Object} [props.validationOptions] - Validation configuration options
 */
export function FormProvider({
  children,
  initialUnitSystem = 'US',
  initialFormData,
  initialParameters,
  initialCipData,
  customUnits,
  onCustomUnitsChange,
  enableValidation = true,
  validationOptions = {}
}) {
  return (
    <FormDataProvider
      initialUnitSystem={initialUnitSystem}
      initialFormData={initialFormData}
      initialParameters={initialParameters}
      initialCipData={initialCipData}
      customUnits={customUnits}
      onCustomUnitsChange={onCustomUnitsChange}
    >
      <FormActionsProvider>
        {enableValidation ? (
          <FormValidationProvider {...validationOptions}>
            {children}
          </FormValidationProvider>
        ) : (
          children
        )}
      </FormActionsProvider>
    </FormDataProvider>
  );
}

/**
 * Memoized Form Provider for performance optimization
 * Prevents unnecessary re-renders when props haven't changed
 */
export const MemoizedFormProvider = memo(FormProvider);

/**
 * Default export - simple FormProvider
 */
export default FormProvider;

/**
 * Export provider components for flexibility
 */
export {
  FormDataProvider,
  FormActionsProvider,
  FormValidationProvider
};
