/**
 * Parameter-specific validation utilities
 * Handles validation for parameter table fields with unit-specific constraints
 */

import { 
  validateRequired, 
  validate<PERSON><PERSON><PERSON>, 
  validate<PERSON><PERSON><PERSON>, 
  validateWithRules,
  createValidationRule,
  isEmpty 
} from './validationCore';
import { getValidationLimits } from '../../config/parameterConfig';
import { getUnitLabel } from '../unitConversion';

/**
 * Parameter field types that should be excluded from validation
 */
export const EXCLUDED_PARAMETER_FIELDS = ['normalized', 'threshold'];

/**
 * Parameter field types that should never show validation errors
 */
export const READ_ONLY_PARAMETER_FIELDS = ['normalized', 'threshold'];

/**
 * Check if a parameter field should be validated
 * @param {string} fieldName - Field name (start, current, limit, normalized, threshold)
 * @param {boolean} isDisabled - Whether the field is disabled
 * @param {boolean} isReadOnly - Whether the field is read-only
 * @returns {boolean} True if field should be validated
 */
export const shouldValidateParameterField = (fieldName, isDisabled = false, isReadOnly = false) => {
  // Skip validation for disabled fields
  if (isDisabled) return false;
  
  // Skip validation for read-only fields
  if (isReadOnly) return false;
  
  // Skip validation for special fields
  if (EXCLUDED_PARAMETER_FIELDS.includes(fieldName)) return false;
  
  return true;
};

/**
 * Check if a parameter field should show validation errors
 * @param {string} fieldName - Field name
 * @param {boolean} isDisabled - Whether the field is disabled
 * @param {boolean} isReadOnly - Whether the field is read-only
 * @returns {boolean} True if field should show errors
 */
export const shouldShowParameterError = (fieldName, isDisabled = false, isReadOnly = false) => {
  // Never show errors for disabled fields
  if (isDisabled) return false;
  
  // Never show errors for read-only fields
  if (isReadOnly) return false;
  
  // Never show errors for special fields
  if (READ_ONLY_PARAMETER_FIELDS.includes(fieldName)) return false;
  
  return true;
};

/**
 * Get validation configuration for a parameter field
 * @param {string} parameterType - Parameter type (feedPressure, dP, etc.)
 * @param {string} fieldName - Field name (start, current, limit)
 * @param {string} unitSystem - Unit system (US, Metric, Custom)
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {Object|null} Validation configuration or null if not found
 */
export const getParameterFieldValidation = (parameterType, fieldName, unitSystem, customUnits = {}) => {
  const validationLimits = getValidationLimits(parameterType, unitSystem, customUnits);
  if (!validationLimits || !validationLimits[fieldName]) {
    return null;
  }
  
  return validationLimits[fieldName];
};

/**
 * Create validation rules for a parameter field
 * @param {string} parameterType - Parameter type
 * @param {string} fieldName - Field name
 * @param {string} unitSystem - Unit system
 * @param {boolean} required - Whether field is required
 * @returns {Array} Array of validation rules
 */
export const createParameterFieldRules = (parameterType, fieldName, unitSystem, required = true) => {
  const rules = [];
  
  // Add required rule if specified
  if (required) {
    rules.push(createValidationRule('required'));
  }
  
  // Add number validation rule
  rules.push(createValidationRule('number'));
  
  // Add range validation if limits exist
  const fieldValidation = getParameterFieldValidation(parameterType, fieldName, unitSystem);
  if (fieldValidation) {
    const { min, max } = fieldValidation;
    const unit = getUnitLabel(parameterType, unitSystem);
    
    rules.push(createValidationRule('range', { min, max, unit }));
  }
  
  return rules;
};

/**
 * Validate a parameter field value
 * @param {any} value - Value to validate
 * @param {string} parameterType - Parameter type
 * @param {string} fieldName - Field name
 * @param {string} unitSystem - Unit system
 * @param {Object} options - Validation options
 * @param {boolean} options.required - Whether field is required (default: true)
 * @param {boolean} options.isDisabled - Whether field is disabled (default: false)
 * @param {boolean} options.isReadOnly - Whether field is read-only (default: false)
 * @returns {ValidationResult} Validation result
 */
export const validateParameterField = (value, parameterType, fieldName, unitSystem, options = {}) => {
  const {
    required = true,
    isDisabled = false,
    isReadOnly = false
  } = options;
  
  // Skip validation if field should not be validated
  if (!shouldValidateParameterField(fieldName, isDisabled, isReadOnly)) {
    return { isValid: true, error: '', value };
  }
  
  // Create validation rules for this field
  const rules = createParameterFieldRules(parameterType, fieldName, unitSystem, required);
  
  // Run validation
  return validateWithRules(value, rules);
};

/**
 * Validate multiple parameter fields
 * @param {Object} parameterData - Parameter data object
 * @param {string} parameterType - Parameter type
 * @param {string} unitSystem - Unit system
 * @param {Object} fieldOptions - Field-specific options
 * @returns {Object} Validation results for each field
 */
export const validateParameterFields = (parameterData, parameterType, unitSystem, fieldOptions = {}) => {
  const results = {};
  
  // Define fields to validate
  const fieldsToValidate = ['start', 'current', 'limit'];
  
  fieldsToValidate.forEach(fieldName => {
    const value = parameterData[fieldName];
    const options = fieldOptions[fieldName] || {};
    
    results[fieldName] = validateParameterField(
      value, 
      parameterType, 
      fieldName, 
      unitSystem, 
      options
    );
  });
  
  return results;
};

/**
 * Check if all parameter fields are valid
 * @param {Object} validationResults - Results from validateParameterFields
 * @returns {boolean} True if all fields are valid
 */
export const areParameterFieldsValid = (validationResults) => {
  return Object.values(validationResults).every(result => result.isValid);
};

/**
 * Get all parameter validation errors
 * @param {Object} validationResults - Results from validateParameterFields
 * @returns {Object} Object with field names as keys and error messages as values
 */
export const getParameterValidationErrors = (validationResults) => {
  const errors = {};
  
  Object.entries(validationResults).forEach(([fieldName, result]) => {
    if (!result.isValid && result.error) {
      errors[fieldName] = result.error;
    }
  });
  
  return errors;
};

/**
 * Validate parameter value with unit-specific constraints
 * @param {any} value - Value to validate
 * @param {string} parameterType - Parameter type
 * @param {string} fieldName - Field name
 * @param {string} unitSystem - Unit system ('US', 'Metric', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {ValidationResult} Validation result
 */
export const validateParameterValue = (value, parameterType, fieldName, unitSystem, customUnits = {}) => {
  // Handle empty values
  if (isEmpty(value)) {
    return {
      isValid: true,
      error: '',
      value: null,
      metadata: { parameterType, fieldName, unitSystem }
    };
  }

  // Validate number format
  const numberResult = validateNumber(value);
  if (!numberResult.isValid) {
    return {
      ...numberResult,
      metadata: { parameterType, fieldName, unitSystem }
    };
  }

  // Get field validation limits
  const fieldValidation = getParameterFieldValidation(parameterType, fieldName, unitSystem, customUnits);
  if (!fieldValidation) {
    return {
      isValid: true,
      error: '',
      value: numberResult.value,
      metadata: { parameterType, fieldName, unitSystem }
    };
  }

  // Validate range with unit-specific constraints
  const { min, max, unit } = fieldValidation;
  const rangeResult = validateRange(numberResult.value, min, max, unit);

  return {
    ...rangeResult,
    metadata: {
      parameterType,
      fieldName,
      unitSystem,
      constraints: { min, max, unit }
    }
  };
};

/**
 * Create parameter validation error message
 * @param {string} fieldName - Field name
 * @param {string} errorType - Error type
 * @param {Object} context - Additional context for error message
 * @returns {string} Error message
 */
export const createParameterErrorMessage = (fieldName, errorType, context = {}) => {
  const { min, max, unit, parameterType } = context;
  
  switch (errorType) {
    case 'required':
      return 'This field is required';
    case 'invalid_number':
      return 'Please enter a valid number';
    case 'min_exceeded':
      return `Value must be at least ${min}${unit ? ' ' + unit : ''}`;
    case 'max_exceeded':
      return `Value must not exceed ${max}${unit ? ' ' + unit : ''}`;
    default:
      return 'Invalid value';
  }
};

/**
 * Sanitize parameter input value
 * @param {any} value - Input value
 * @param {string} fieldName - Field name
 * @returns {any} Sanitized value
 */
export const sanitizeParameterInput = (value, fieldName) => {
  // For threshold fields, preserve as-is (may contain percentages)
  if (fieldName === 'threshold') {
    return value;
  }
  
  // For numeric fields, remove non-numeric characters except decimal point and minus
  if (typeof value === 'string') {
    return value.replace(/[^0-9.-]/g, '');
  }
  
  return value;
};
