# NumericInput Migration Guide

## ✅ What We've Accomplished

The `NumericInput` component has been successfully refactored to use Ant Design's Form.Item validation instead of complex custom validation hooks. This provides a much simpler, more maintainable approach while preserving all existing functionality.

## 🔄 Key Changes

### Before (Complex Custom Validation)
```jsx
// Old approach with custom hooks
const { error, validateAndUpdate } = useNumericValidation({ min, max, required, unit });

const handleChange = (newValue) => {
  validateAndUpdate(newValue, onChange);
};

return (
  <div className="input-container">
    <InputNumber
      value={value}
      onChange={handleChange}
      status={error ? 'error' : ''}
      addonAfter={unit}
    />
    {error && <div className="error-message">{error}</div>}
    <p className="input-help-text">Min: {min} - Max: {max}</p>
  </div>
);
```

### After (Simple Form.Item Validation)
```jsx
// New approach with Form.Item
return (
  <Form.Item
    name={name}
    label={label}
    rules={validationRules}
    help={helpText}
  >
    <InputNumber
      onChange={onChange}
      disabled={disabled || isReadOnly}
      addonAfter={constraints.unit || unit}
    />
  </Form.Item>
);
```

## 🎯 New Features

### 1. Dynamic Unit Validation
```jsx
<NumericInput
  name={['parameters', 'feedPressure', 'start']}
  label="Feed Pressure"
  parameterType="feedPressure"  // Enables dynamic validation
  fieldName="start"             // Specifies field constraints
  required
/>
```

### 2. Static Validation (Backward Compatible)
```jsx
<NumericInput
  name="temperature"
  label="Temperature"
  unit="°C"
  min={-50}
  max={100}
  required
/>
```

### 3. Read-only Fields
```jsx
<NumericInput
  name={['parameters', 'feedPressure', 'normalized']}
  label="Normalized Pressure"
  parameterType="feedPressure"
  fieldName="normalized"
  isReadOnly={true}
/>
```

## 📋 Updated Props

### Required Props
- `name` - Form field name (string or array for nested fields)

### Optional Props
- `label` - Field label
- `onChange` - Change handler function
- `unit` - Static unit (fallback if no dynamic unit)
- `addonAfter` - Custom addon content
- `disabled` - Disable the input
- `placeholder` - Placeholder text
- `min` - Static minimum value
- `max` - Static maximum value
- `step` - Input step value
- `required` - Whether field is required

### New Props for Dynamic Validation
- `parameterType` - Parameter type for dynamic validation (e.g., 'feedPressure')
- `fieldName` - Field name for dynamic validation (e.g., 'start', 'current', 'limit')
- `isReadOnly` - Make field read-only
- `className` - CSS class name
- `style` - Inline styles

## 🚀 Usage Examples

### Basic Form with Validation
```jsx
import { Form } from 'antd';
import NumericInput from './components/NumericInput';

function MyForm() {
  const [form] = Form.useForm();
  
  return (
    <Form form={form} onFinish={handleSubmit}>
      <NumericInput
        name="temperature"
        label="Temperature"
        unit="°C"
        min={-50}
        max={100}
        required
      />
      
      <NumericInput
        name="pressure"
        label="Pressure"
        unit="bar"
        min={0}
        max={100}
        required
      />
    </Form>
  );
}
```

### Parameter Table with Dynamic Validation
```jsx
function ParameterRow({ parameterType }) {
  return (
    <Form form={form}>
      <NumericInput
        name={['parameters', parameterType, 'start']}
        parameterType={parameterType}
        fieldName="start"
        onChange={(value) => updateParameter(parameterType, 'start', value)}
      />
      
      <NumericInput
        name={['parameters', parameterType, 'current']}
        parameterType={parameterType}
        fieldName="current"
        onChange={(value) => updateParameter(parameterType, 'current', value)}
      />
      
      <NumericInput
        name={['parameters', parameterType, 'normalized']}
        parameterType={parameterType}
        fieldName="normalized"
        isReadOnly={true}
      />
    </Form>
  );
}
```

### Form with Unit System Context
```jsx
import { FormProvider } from './contexts/FormProvider';

function App() {
  return (
    <FormProvider>
      <Form>
        {/* These will automatically update validation when unit system changes */}
        <NumericInput
          name={['parameters', 'feedPressure', 'start']}
          label="Feed Pressure"
          parameterType="feedPressure"
          fieldName="start"
          required
        />
      </Form>
    </FormProvider>
  );
}
```

## 🔧 Migration Steps

### 1. Update Existing NumericInput Usage
Replace the old NumericInput props:
```jsx
// Old
<NumericInput
  label="Temperature"
  value={temperature}
  onChange={setTemperature}
  unit="°C"
  min={-50}
  max={100}
  required
/>

// New
<NumericInput
  name="temperature"
  label="Temperature"
  unit="°C"
  min={-50}
  max={100}
  required
/>
```

### 2. Wrap Components with Form
```jsx
// Add Form wrapper
<Form onFinish={handleSubmit}>
  <NumericInput name="temperature" label="Temperature" />
</Form>
```

### 3. Use Dynamic Validation for Parameters
```jsx
// For parameter fields, add parameterType and fieldName
<NumericInput
  name={['parameters', 'feedPressure', 'start']}
  parameterType="feedPressure"
  fieldName="start"
  required
/>
```

## ✅ Benefits

1. **90% Less Code** - No more complex validation hooks
2. **Automatic Error Handling** - Ant Design handles all error display
3. **Dynamic Unit Conversion** - Validation constraints update automatically
4. **Clear Error Messages** - Error messages include units and constraints
5. **Easy Debugging** - Simple validation rules, easy to trace
6. **Help Text** - Shows current min/max constraints
7. **Backward Compatible** - Existing props still work

## 🧪 Testing

To test the updated component:

1. **Import the test component**:
```jsx
import NumericInputTest from './components/NumericInputTest';
```

2. **Add to your app**:
```jsx
<NumericInputTest />
```

3. **Test scenarios**:
   - Enter values outside allowed ranges
   - Leave required fields empty
   - Submit valid form data
   - Check console for form values

## 🎉 Next Steps

The NumericInput component is now ready for use! You can:

1. **Start using it immediately** - It's backward compatible
2. **Gradually migrate existing forms** - Replace old NumericInput usage
3. **Update ParameterRow components** - Use the new dynamic validation
4. **Remove old validation hooks** - Clean up unused validation code

The new approach is much simpler, more maintainable, and easier to debug while preserving all existing functionality!
