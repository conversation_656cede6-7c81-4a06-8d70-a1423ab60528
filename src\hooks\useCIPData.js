import { useState } from 'react';
import { DEFAULT_VALUES, CIP_CONFIG } from '../constants';

/**
 * Custom hook for managing CIP (Clean-in-Place) data
 * Provides state management and validation for CIP settings
 */
export function useCIPData() {
  const [cipData, setCipData] = useState({
    frequency: DEFAULT_VALUES.CIP_FREQUENCY,
    duration: DEFAULT_VALUES.CIP_DURATION
  });

  /**
   * Update CIP frequency with validation
   * @param {number} frequency - New frequency value
   */
  const updateFrequency = (frequency) => {
    const validatedFrequency = Math.max(
      CIP_CONFIG.FREQUENCY_MIN,
      Math.min(CIP_CONFIG.FREQUENCY_MAX, Number(frequency) || 0)
    );
    
    setCipData(prev => ({
      ...prev,
      frequency: validatedFrequency
    }));
  };

  /**
   * Update CIP duration
   * @param {string|number} duration - New duration value
   */
  const updateDuration = (duration) => {
    setCipData(prev => ({
      ...prev,
      duration: duration
    }));
  };

  /**
   * Reset CIP data to default values
   */
  const resetCIPData = () => {
    setCipData({
      frequency: DEFAULT_VALUES.CIP_FREQUENCY,
      duration: DEFAULT_VALUES.CIP_DURATION
    });
  };

  /**
   * Validate CIP data
   * @returns {Object} Validation result with isValid flag and errors
   */
  const validateCIPData = () => {
    const errors = [];
    
    if (cipData.frequency < CIP_CONFIG.FREQUENCY_MIN || cipData.frequency > CIP_CONFIG.FREQUENCY_MAX) {
      errors.push(`Frequency must be between ${CIP_CONFIG.FREQUENCY_MIN} and ${CIP_CONFIG.FREQUENCY_MAX}`);
    }
    
    if (!cipData.duration || cipData.duration === '') {
      errors.push('Duration is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };

  return {
    cipData,
    updateFrequency,
    updateDuration,
    resetCIPData,
    validateCIPData
  };
}
