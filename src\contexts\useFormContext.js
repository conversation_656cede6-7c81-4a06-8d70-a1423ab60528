/**
 * Form Context Hooks
 * Custom hooks for accessing form context
 */

import { useContext, useMemo } from 'react';
import {
  FormDataContext,
  FormActionsContext,
  FormValidationContext,
  CONTEXT_ERROR_MESSAGES
} from './FormContext.jsx';

/**
 * Hook for accessing form data context
 * Provides read-only access to form state
 * 
 * @returns {Object} Form data context value
 * @throws {Error} If used outside FormProvider
 * 
 * @example
 * const { formData, parameters, unitSystem } = useFormData();
 * const startTemperature = formData.start.temperature;
 */
export function useFormData() {
  const context = useContext(FormDataContext);
  
  if (!context) {
    throw new Error(CONTEXT_ERROR_MESSAGES.FORM_DATA_NOT_FOUND);
  }
  
  return context;
}

/**
 * Hook for accessing form actions context
 * Provides functions to update form state
 * 
 * @returns {Object} Form actions context value
 * @throws {Error} If used outside FormProvider
 * 
 * @example
 * const { updateForm<PERSON>ield, updateParameter } = useFormActions();
 * updateFormField('start', 'temperature', '25');
 */
export function useFormActions() {
  const context = useContext(FormActionsContext);
  
  if (!context) {
    throw new Error(CONTEXT_ERROR_MESSAGES.FORM_ACTIONS_NOT_FOUND);
  }
  
  return context;
}

/**
 * Hook for accessing form validation context
 * Provides validation state and utilities
 * 
 * @returns {Object} Form validation context value
 * @throws {Error} If used outside FormProvider
 * 
 * @example
 * const { validateField, getFieldError, isFormValid } = useFormValidation();
 * const error = getFieldError('formData.start.temperature');
 */
export function useFormValidation() {
  const context = useContext(FormValidationContext);
  
  if (!context) {
    throw new Error(CONTEXT_ERROR_MESSAGES.FORM_VALIDATION_NOT_FOUND);
  }
  
  return context;
}

/**
 * Hook for accessing all form contexts
 * Provides combined access to data, actions, and validation
 * 
 * @returns {Object} Combined form context
 * @throws {Error} If used outside FormProvider
 * 
 * @example
 * const { data, actions, validation } = useFormContext();
 * const temperature = data.formData.start.temperature;
 * actions.updateFormField('start', 'temperature', '30');
 */
export function useFormContext() {
  const data = useFormData();
  const actions = useFormActions();
  const validation = useFormValidation();
  
  return useMemo(() => ({
    data,
    actions,
    validation
  }), [data, actions, validation]);
}

/**
 * Safe hook variants that return null instead of throwing errors
 * Useful for optional form context usage
 */
export function useSafeFormData() {
  try {
    return useFormData();
  } catch {
    return null;
  }
}

export function useSafeFormActions() {
  try {
    return useFormActions();
  } catch {
    return null;
  }
}

export function useSafeFormValidation() {
  try {
    return useFormValidation();
  } catch {
    return null;
  }
}
