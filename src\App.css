/* ===== SIMPLIFIED CSS - CONSOLIDATED STYLES ===== */

/* Reset & Variables */
* {
  box-sizing: border-box;
}

:root {
  /* --primary-color:  #128370; */
  --brand-500: #128370;
  --gray-25: #fcfcfd;
  --gray-100: #f2f4f7;
  --gray-400: #98a2b3;
  --gray-500: #667085;
  --gray-600: #475467;
  --gray-700: #344054;
  --gray-800: #1d2939;
  --gray-900: #101828;
  --white: #ffffff;
  --gray-e1: #e1e1e1;
  --gray-96: #969696;
  --gray-f8: #f8f8f8;
  --gray-f0: #f0f0f0;
  --gray-200: #e4e7ec;
  --teal-pale: #e2f7f5;
  --blue-border: #1890ff;
  /* --grey-pale:#F8F8F8; */

  /* Typography Scale */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* Fonts */
@font-face {
  font-family: 'Diodrum';
  src: local('Diodrum');
  font-weight: 400;
}
@font-face {
  font-family: 'Diodrum';
  src: local('Diodrum-Medium');
  font-weight: 500;
}
@font-face {
  font-family: 'Diodrum';
  src: local('Diodrum-Semibold');
  font-weight: 600;
}
@font-face {
  font-family: 'Diodrum';
  src: local('Diodrum-Bold');
  font-weight: 700;
}

/* Base Styles */
body {
  margin: 0;
  font-family: 'Diodrum', sans-serif;
  background-color: var(--white);
  overflow-x: hidden;
  /* min-height: 100vh; */
  /* height: 300vh; */
}

#root {
  width: 100%;
  /* min-height: 100vh; */
  padding: 16px;
  max-width: 1460px;
  margin: 0 auto;
}

/* Main Layout */
.ro-advisor {
  width: 100%;
  background: var(--white);
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* min-height: calc(100vh - 32px); */
  /* overflow-x: scroll; */
}

.main-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  /* overflow-x: hidden; */
}

/* Header */
.header {
  margin-bottom: var(--spacing-lg);
  text-align: left;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: var(--brand-500);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1.2;
}

.description {
  font-size: var(--font-size-base);
  font-weight: 400;
  color: var(--gray-500);
  line-height: 1.5;
  margin: 0;
  width: 100%;
  /* max-width: 600px; */
}

/* Settings Section */
.settings-section {
  margin-bottom: 32px;
  align-items: center;
  justify-content: space-between;
}

.settings-row {
  margin-bottom: 24px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  min-height: 60px;
}

.setting-group h2,
.setting-title {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--gray-700);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: calc(var(--font-size-base) * var(--line-height-normal));
  white-space: nowrap;
}

.required {
  color: #e53e3e;
  margin-right: 4px;
}

/* CIP Input Groups */
.cip-input-wrapper {
  display: flex;
  align-items: stretch;
  border: 1px solid var(--gray-e1);
  border-radius: 4px;
  overflow: hidden;
  height: 32px;
  width: 100%;
  background: var(--white);
}

.cip-decrement-btn {
  background: var(--gray-100);
  border: none;
  border-right: 1px solid var(--gray-e1);
  width: 36px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-600);
  transition: background-color 0.2s;
}

.cip-decrement-btn:hover {
  background: var(--gray-200);
}

.cip-increment-btn {
  background: var(--gray-100);
  border: none;
  border-left: 1px solid var(--gray-e1);
  width: 34px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-600);
}

.cip-number-input {
  flex: 1;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  height: 30px !important;
}

.cip-number-input .ant-input-number-input {
  text-align: center;
  padding: 0 8px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  border: none;
  height: 30px;
}

.cip-number-input:focus,
.cip-number-input:focus-within {
  border: none !important;
  box-shadow: none !important;
}

.cip-increment-btn {
  background: var(--gray-100);
  border: none;
  border-left: 1px solid var(--gray-e1);
  width: 34px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-600);
  transition: background-color 0.2s;
}

.cip-increment-btn:hover {
  background: var(--gray-200);
}

.cip-unit-label {
  padding: 0 8px;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  background: var(--gray-100);
  border-left: 1px solid var(--gray-e1);
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.required {
  color: #f04438;
  margin-left: 2px;
}

/* Button Group */
.button-group {
  display: flex;
  background: var(--gray-100);
  border: 1px solid var(--gray-e1);
  border-radius: 10px;
  overflow: hidden;
  width: 100%;
  height: 32px; /* Consistent height with membrane type buttons */
}

/* Option Selector - Reusable component following ButtonGroup pattern */
.option-selector {
  display: flex;
  background: var(--gray-100);
  border: 1px solid var(--gray-e1);
  border-radius: 10px;
  overflow: hidden;
  width: 100%;
  height: 32px; /* Consistent height with other form components */
}

.option-selector-small {
  height: 28px;
}

.option-selector-medium {
  height: 32px;
}

.option-selector-large {
  height: 40px;
}

.option-selector-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-group-item {
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-600);
  background: var(--white);
  border: none;
  border-right: 1px solid var(--gray-e1);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
}

.button-group-item:last-child {
  border-right: none;
}

.button-group-item:hover:not(.selected) {
  background: var(--gray-25);
}

.button-group-item.selected {
  background: var(--brand-500);
  color: var(--white);
  font-weight: 600;
  border-color: var(--brand-500);
}

/* Option Selector Items - Following ButtonGroup pattern */
.option-selector-item {
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-600);
  background: var(--white);
  border: none;
  border-right: 1px solid var(--gray-e1);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
}

.option-selector-item:last-child {
  border-right: none;
}

.option-selector-item:hover:not(.selected):not(:disabled) {
  background: var(--gray-25);
}

.option-selector-item.selected {
  background: #007672;
  color: var(--white);
  font-weight: 600;
  border-color: var(--brand-500);
}

.option-selector-item:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.option-selector-disabled .option-selector-item {
  cursor: not-allowed;
  opacity: 0.6;
}

.custom-unit-flow-2-row {
  margin-top: -10px;
}

/* Grid Layout */
.data-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.grid-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.grid-column h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: calc(var(--font-size-lg) * var(--line-height-normal));
  text-align: center;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--gray-e1);
}

/* Data Input Grid */
.data-input-grid {
  margin-bottom: var(--spacing-xl);
  padding-bottom: 45px;
  border-bottom: 1px solid var(--gray-e1);
}

.data-column {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: 8px;
  min-height: 300px;
  position: relative;
  box-sizing: border-box;
}

.data-column-start {
  background: #f8f8f8;
  border: 1px solid #e6f7f5;
}

.data-column-current {
  background: #f8f8f8;
  border: 1px solid #e8e8e8;
}

.data-column-forecast {
  background: #f8f8f8;
  border: 1px solid #e6f7f5;
}

.column-header {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--spacing-md) 0;
  text-align: center;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--gray-e1);
}

.input-row {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  width: 100%;
}

/* Additional override for Ant Design input number hover states */
.unified-input-addon .ant-input-number-sm:hover,
.unified-input-addon .ant-input-number-outlined:hover {
  border-color: var(--gray-e1) !important;
}

/* Unified Input Addon Styling - Cleaned and Enhanced */
.unified-input-addon {
  width: 100%;
  height: 32px;
  display: flex;
  align-items: stretch;
  border-radius: 4px !important;
  transition: box-shadow 0.2s ease;
}

.unified-input-addon.ant-input-group-wrapper {
  display: flex;
  align-items: stretch;
  width: 100%;
  border-radius: 4px !important;
}

.unified-input-addon .ant-input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
  border-radius: 4px !important;
}

/* Input field styling - left border radius for seamless connection */
.unified-input-addon .ant-input,
.unified-input-addon .ant-input-number {
  height: 32px;
  border-radius: 4px 0 0 4px !important;
  border-right: none;
  flex: 1;
}

.unified-input-addon .ant-input-number-input {
  height: 32px;
  padding: 4px 11px;
  border: none;
}

/* Remove hover border color changes for input components */
.unified-input-addon .ant-input:hover,
.unified-input-addon .ant-input-number:hover,
.unified-input-addon .ant-input-number-outlined:hover {
  border-color: var(--gray-e1) !important;
}

/* Remove focus border color changes for individual input components */
.unified-input-addon .ant-input:focus,
.unified-input-addon .ant-input-number:focus,
.unified-input-addon .ant-input-focused,
.unified-input-addon .ant-input-number-focused {
  border-color: var(--gray-e1) !important;
  box-shadow: none !important;
}

/* Comprehensive override for all Ant Design input hover/focus states within unified addon */
.unified-input-addon .ant-input-number-affix-wrapper:hover,
.unified-input-addon .ant-input-affix-wrapper:hover,
.unified-input-addon .ant-input-number-wrapper:hover {
  border-color: var(--gray-e1) !important;
}

/* Addon styling - seamless connection with input */
.unified-input-addon .ant-input-group-addon,
.unified-input-addon .ant-input-number-group-addon {
  background: var(--gray-100);
  border-color: var(--gray-e1);
  border-left: none;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  font-weight: 500;
  min-width: 50px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 4px 4px 0 !important;
  line-height: 1;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

/* Hover state - subtle box shadow only */
.unified-input-addon:hover {
  box-shadow: 0 2px 4px rgba(194, 180, 255, 0.329);
}

/* Focus state - unified border around entire component */
.unified-input-addon:focus-within {
  box-shadow: 0 0 2px 2px rgba(22, 119, 255, 0.1);
}

.unified-input-addon:focus-within .ant-input,
.unified-input-addon:focus-within .ant-input-number {
  border-color: var(--brand-500) !important;
  box-shadow: none !important;
}

.unified-input-addon:focus-within .ant-input-group-addon,
.unified-input-addon:focus-within .ant-input-number-group-addon {
  border-color: var(--brand-500) !important;
}

/* Ensure focus-within overrides individual focus states */
.unified-input-addon:focus-within .ant-input-number-affix-wrapper,
.unified-input-addon:focus-within .ant-input-affix-wrapper,
.unified-input-addon:focus-within .ant-input-number-wrapper {
  border-color: var(--brand-500) !important;
}

/* Error state styling for unified input addon when focused */
.ant-form-item-has-error .unified-input-addon:focus-within {
  box-shadow: 0 0 2px 2px rgba(255, 77, 79, 0.2) !important;
}

.ant-form-item-has-error .unified-input-addon:hover {
  box-shadow: 0 0 2px 2px rgba(255, 77, 79, 0.2) !important;
}

.ant-form-item-has-error .unified-input-addon:focus-within .ant-input,
.ant-form-item-has-error .unified-input-addon:focus-within .ant-input-number {
  border-color: #ff4d4f !important;
  box-shadow: none !important;
}

/* on hover  */
.ant-form-item-has-error .unified-input-addon:hover .ant-input,
.ant-form-item-has-error .unified-input-addon:hover .ant-input-number {
  border-color: #ff4d4f !important;
  box-shadow: none !important;
}

.ant-form-item-has-error
  .unified-input-addon:focus-within
  .ant-input-group-addon,
.ant-form-item-has-error
  .unified-input-addon:focus-within
  .ant-input-number-group-addon {
  border-color: #ff4d4f !important;
}

/* Ensure error focus-within overrides all other focus states */
.ant-form-item-has-error
  .unified-input-addon:focus-within
  .ant-input-number-affix-wrapper,
.ant-form-item-has-error
  .unified-input-addon:focus-within
  .ant-input-affix-wrapper,
.ant-form-item-has-error
  .unified-input-addon:focus-within
  .ant-input-number-wrapper {
  border-color: #ff4d4f !important;
}

.hidden-date-input {
  width: 1px;
  height: 1px;
}

.input-help-text {
  font-size: 10px;
  color: var(--gray-500);
  margin-top: 0px;
  line-height: 12px;
}

.status-cards-row {
  margin-bottom: 24px;
}

/* Responsive adjustments for status cards row */
@media (max-width: 576px) {
  .status-cards-row {
    margin-bottom: 16px;
  }

  .status-cards-row .ant-col {
    margin-bottom: 12px;
  }

  .status-cards-row .ant-col:last-child {
    margin-bottom: 0;
  }
}

/* Parameters Table */
.parameters-table {
  margin: var(--spacing-xl) 0;
  /* border: 1px solid var(--gray-e1); */
  border-radius: 4px;
  /* overflow: hidden; */
}

/* Parameter Table Styles */
.parameter-table-container {
  margin-bottom: 30px;
  width: 100%;
}

.parameter-table-header {
  padding: 12px 0;
  border-bottom: 1px solid var(--gray-e1);
  min-width: 600px;
}

.parameter-header-cell {
  text-align: center;
  padding: 0 8px;
}

.parameter-name-header {
  background: transparent;
}

.parameter-header-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-700);
  line-height: 16px;
}

.parameter-table-body {
  width: 100%;
}

.parameter-table-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  min-width: 600px;
  position: relative;
  margin-bottom: 24px; /* Increased to accommodate error messages */
}

/* Label styling */
.parameter-label-container {
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.parameter-name-text {
  font-size: 12px;
  color: var(--gray-900);
  font-weight: 600;
  line-height: 14px;
}

/* Parameter Input Styles - Cleaned and Unified */
.parameter-table-input {
  width: 100% !important;
  height: 32px !important;
  border-radius: 4px;
  transition: box-shadow 0.2s ease;
  border: 1px solid var(--gray-e1) !important;
  overflow: hidden; /* Keep inner elements from breaking the border */
}

/* Remove borders from inner elements */
.parameter-table-input .ant-input-number {
  border: none !important;
  box-shadow: none !important;
}

.parameter-table-input .ant-input-number-group-addon {
  border: none !important;
  box-shadow: none !important;
  background: var(--gray-f8);
}

/* Error state for the unified element */
.parameter-table-input.input-error {
  width: auto !important;
  height: 32px !important;
  border-color: #ff4d4f !important;
}

/* Focus state for the unified element */
.parameter-table-input:focus-within {
  border-color: var(--brand-500) !important;
  box-shadow: 0 0 2px 2px rgba(22, 119, 255, 0.1);
}

/* Error focus state for parameter table inputs */
.ant-form-item-has-error .parameter-table-input:focus-within {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 2px 2px rgba(255, 77, 79, 0.2) !important;
}

.ant-form-item-has-error .parameter-table-input:focus-within .ant-input-number {
  border-color: #ff4d4f !important;
}

.ant-form-item-has-error
  .parameter-table-input:focus-within
  .ant-input-number-group-addon {
  border-color: #ff4d4f !important;
}

.parameter-table-input .ant-input-number-input {
  font-size: 12px;

  color: var(--gray-900);
  text-align: left; /* Align input values to the left */
  padding-top: 8px !important;
  border: none;
}

.parameter-table-input .ant-input-number-group-addon {
  font-size: 10px;
  font-weight: 500;
  color: var(--gray-600);
  background: var(--gray-f8);
  /* border-left: none; */
  padding: 0 8px;
  min-width: 40px;
  text-align: left;
}

/* Hover state for parameter inputs - subtle box shadow only */
.parameter-table-input:hover {
  box-shadow: 2px 2px 2px 0px rgba(22, 119, 255, 0.1);
}

/* Focus state - unified border around entire parameter input component */
.parameter-table-input:focus-within {
  box-shadow: 2px 2px 2px 0px rgba(22, 119, 255, 0.1);
  border-color: var(--brand-500) !important;
}

.parameter-table-input:focus-within .ant-input-number-group-addon {
  border-color: var(--brand-500) !important;
}

.parameter-table-input:focus-within .ant-input-number {
  /* border-color: var(--brand-500) !important; */
  box-shadow: none !important;
}

.parameter-table-input:focus-within .ant-input-number-group-addon {
}

/* Normalized Input Styles - Blue border for special fields */
.parameter-table-input-normalized {
  /* border: 1px solid #00aaff !important; */
  border: 1px solid var(--blue-border) !important;
  box-shadow: none !important;
  border-radius: 4px !important;
}

.parameter-table-input-normalized .ant-input-number-input {
  background: var(--gray-f8);
  box-shadow: none !important;

  color: #667085d7 !important;
}

.parameter-table-input-normalized .ant-input-number {
  border: none !important;
  background: var(--gray-f8);
  box-shadow: none !important;
  height: 30px !important;
  /* border-color: #00aaff !important; */
}

.parameter-table-input-normalized .ant-input-number-group-addon {
  /* border-color: #00aaff !important; */
  border: none !important;
  box-shadow: none !important;
  /* //remove box shadow */
  /* border-top: #00aaff !important; */
  /* border- */
  /* border-left: none !important; */

  color: #667085d7 !important;
  background: var(--gray-f8);
}

.parameter-table-input-normalized:hover {
  box-shadow: none !important;
}

/* Threshold Input Styles - Blue border for special fields */
.parameter-table-input-threshold {
  border: none !important;
  /* border: 1px solid #00aaff !important; */
  border-radius: 4px !important;
  border: 1px solid var(--blue-border) !important;
}

.parameter-table-input-threshold .ant-input-number-input {
  background: var(--gray-f8);
  /* border: none !important; */
  color: var(--gray-300);
}

.parameter-table-input-threshold .ant-input-number-group-addon {
  background: var(--gray-f8);
  box-shadow: none !important;
}

.parameter-table-input-threshold:focus-within {
  border: none !important;
  /* box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.719) !important; */
}

/* Threshold inputs should keep blue border even when focused with errors */
.ant-form-item-has-error .parameter-table-input-threshold:focus-within {
  border: 1px solid var(--blue-border) !important;
  box-shadow: none !important;
}

.ant-form-item-has-error
  .parameter-table-input-threshold:focus-within
  .ant-input-number,
.ant-form-item-has-error
  .parameter-table-input-threshold:focus-within
  .ant-input-number-group-addon {
  border-color: var(--blue-border) !important;
  box-shadow: none !important;
}

/* Focus state handled by unified styles above */

/* Responsive Styles */
@media (min-width: 768px) {
  .parameter-table-row {
    gap: 16px;
    padding: 12px 0;
  }

  .parameter-table-input {
    height: 32px !important;
  }

  .parameter-table-input .ant-input-number-input {
    height: 32px;
    font-size: 13px;
  }
}

/* Mobile-first responsive improvements */
@media (max-width: 767px) {
  /* .settings-row {
    flex-direction: column;
    gap: var(--spacing-md);
  } */

  .data-input-grid {
    flex-direction: column;
  }

  .data-column {
    margin-bottom: var(--spacing-md);
    position: relative;
    min-height: unset;
  }

  .parameters-table {
    overflow-x: auto;
    margin: var(--spacing-lg) -16px;
    padding: 0 16px;
  }

  .table-header,
  .table-row {
    min-width: 600px;
  }

  /* Parameter Table Mobile Styles */
  .parameter-table-container {
    overflow-x: auto;
    margin: var(--spacing-lg) -16px;
    padding: 0 16px;
  }

  .parameter-table-header,
  .parameter-table-row {
    min-width: 600px;
  }

  .calculate-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (min-width: 768px) {
  .data-column {
    min-height: 350px;
    position: relative;
  }
}

/* Footer and Disclaimer */
.footer {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--gray-e1);
}

.disclaimer {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  max-width: 800px;
}

.disclaimer-icon {
  color: var(--gray-400);
  font-size: var(--font-size-lg);
  line-height: 20px;
  flex-shrink: 0;
  margin-top: 1px;
}

.disclaimer-text {
  font-size: var(--font-size-sm);
  font-weight: 400;
  color: var(--gray-500);
  line-height: 18px;
}

/* Ant Design Component Standardization - Cleaned */
.ant-input-number:not(.unified-input-addon .ant-input-number),
.ant-input:not(.unified-input-addon .ant-input),
.ant-picker {
  width: 200px;
  height: 32px;
  /* top left bottom left border radius  ; */
  border-bottom-left-radius: 4px;
  border: 1px solid var(--gray-e1);
  transition: box-shadow 0.2s ease;
}

.ant-input-number-input:not(.unified-input-addon .ant-input-number-input),
.ant-picker-input {
  /* height: 30px; */
  font-size: var(--font-size-sm);
  font-weight: 500;
  /* padding-top: 8px; */
  /* padding: 1px 10px; */
}

/* Consistent hover states - subtle box shadow only */
.ant-input-number:not(.unified-input-addon .ant-input-number):hover,
.ant-input:not(.unified-input-addon .ant-input):hover,
.ant-picker:hover {
  /* box-shadow: 0 2px 8px rgb/a(154, 169, 255, 0.555); */
}

/* Consistent focus states */
.ant-input-number:not(.unified-input-addon .ant-input-number):focus,
.ant-input:not(.unified-input-addon .ant-input):focus,
.ant-picker-focused {
  /* border-color: var(--brand-500); */
  /* box-shadow: 0 2px 8px rgba(154, 169, 255, 0.555); */
}

/* Error focus states for standalone inputs (not using unified-input-addon) */
.ant-form-item-has-error
  .ant-input-number:not(.unified-input-addon .ant-input-number):focus,
.ant-form-item-has-error .ant-input:not(.unified-input-addon .ant-input):focus {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

/* Input Components - Consolidated */
.input-field {
  display: flex;
  align-items: center;
  background: var(--white);
  border: 1px solid var(--gray-e1);
  /* border-radius: 4px !important; */
  overflow: visible;
  height: 32px;
  width: 200px;
  max-width: 100%;
}

.input-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 4px 10px;
  height: 32px;
  justify-content: center;
}

.input-label {
  font-size: 11px;
  font-weight: 500;
  color: var(--gray-500);
  line-height: 14px;
  margin-bottom: 2px;
}

.input-field-input,
.simple-input {
  border: none;
  outline: none;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-900);
  background: transparent;
  padding: 0;
  line-height: calc(var(--font-size-sm) * var(--line-height-normal));
  height: 32px;
  box-sizing: border-box;
}

.simple-input {
  flex: 1;
  padding: 8px 12px;
  font-size: 14px;
}

.input-field-input:disabled,
.simple-input:disabled {
  color: var(--gray-96);
  background: var(--gray-f8);
}

.input-field-input::placeholder {
  color: var(--gray-400);
}

.input-unit,
.simple-unit {
  display: flex !important;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  font-size: 13px;
  font-weight: 500;
  /* width: 20 */
  color: var(--gray-600);
  background: var(--gray-f8);
  border-left: 1px solid var(--gray-e1);
  height: 32px;
  min-width: 50px;
  max-width: 80px;
  text-align: center;
  flex-shrink: 0;
}

.simple-unit {
  padding: 0 12px;
  font-size: 13px;
  color: var(--gray-600);
  font-weight: 500;
}

.input-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 32px;
  background: var(--gray-f8);
  border-left: 1px solid var(--gray-e1);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.input-action:hover {
  background: var(--gray-e1);
}

.input-action:active {
  background: var(--gray-400);
}

.action-icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.input-action:hover .action-icon {
  opacity: 1;
}

/* Date Picker Specific Styles */
.hidden-date-input {
  position: absolute !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -1 !important;
  width: 0 !important;
  height: 0 !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure the input content container is positioned relative for absolute positioning */
.input-content {
  position: relative;
}

/* Parameters Grid */
.divider {
  height: 1px;
  background: var(--gray-e1);
  margin: 32px 0;
}

.parameters-grid {
  width: 100%;
  margin-bottom: 24px;
  overflow-x: auto;
}

.grid-header {
  display: grid;
  grid-template-columns: 100px repeat(5, minmax(80px, 1fr));
  gap: 8px;
  padding: 12px 0;
  border-bottom: 1px solid var(--gray-e1);
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-700);
  text-align: center;
  min-width: 600px;
}

.grid-header > div:first-child {
  text-align: left;
}

.parameter-row {
  display: grid;
  grid-template-columns: 100px repeat(5, minmax(80px, 1fr));
  gap: 8px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--gray-f0);
  min-width: 600px;
  position: relative;
  margin-bottom: 24px; /* Increased to accommodate error messages */
}

.parameter-name {
  font-size: 12px;
  color: var(--gray-900);
  font-weight: 600;
  line-height: 14px;
}

.parameter-unit {
  font-size: 10px;
  color: var(--gray-600);
  font-weight: 500;
  line-height: 12px;
}

.parameter-input {
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--gray-e1);
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  color: var(--gray-900);
  background: var(--white);
  outline: none;
  transition: border-color 0.2s;
}

.parameter-input:focus {
  border-color: var(--brand-500);
}

.parameter-input.normalized {
  background: var(--gray-100);
  color: var(--gray-600);
}

.parameter-input.threshold {
  background: var(--gray-25);
}

.parameter-input:disabled {
  background: var(--gray-f8);
  color: var(--gray-500);
  cursor: not-allowed;
}

/* Status Card Styles */
.status-card {
  background: var(--white);
  border: 1px solid var(--gray-e1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 0;
  height: 100%;
}

.status-card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.status-card-left {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

/* Responsive adjustments for small screens */
@media (max-width: 576px) {
  .status-card {
    padding: 12px;
    min-height: 100px;
  }

  .status-card-content {
    flex-direction: column;
    gap: 12px;
  }

  .status-card-left {
    gap: 8px;
  }

  .status-card-right {
    align-items: flex-start !important;
    text-align: left !important;
    width: 100%;
  }
}

.status-icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: var(--gray-100); */
  border-radius: 8px;
  flex-shrink: 0;
}

.status-icon-img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-700);
  margin: 0;
  line-height: 20px;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 4px 0;
  line-height: 24px;
}

.status-indicator {
  display: flex;
  align-items: center;
  background-color: #ecfdf3;
  border-radius: 16px;
  padding: 4px 8px;
  gap: 6px;
}

/* Responsive text adjustments for small screens */
@media (max-width: 576px) {
  .status-title {
    font-size: 14px;
    line-height: 18px;
  }

  .status-value {
    font-size: 16px;
    line-height: 22px;
    margin: 2px 0;
  }

  .status-indicator {
    padding: 3px 6px;
    gap: 4px;
  }
}

.status-icon-small {
  font-size: 12px;
  font-weight: 600;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
}

.status-card-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.estimated-life-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.estimated-life-label {
  font-size: 12px;
  font-weight: 400;
  color: var(--gray-400);
  line-height: 18px;
}

.life-details {
  display: flex;
  align-items: center;
  gap: 4px;
}

.calendar-icon {
  font-size: 12px;
  color: var(--gray-400);
}

.life-date {
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-700);
  line-height: 18px;
}

.life-duration {
  font-size: 12px;
  font-weight: 400;
  color: var(--gray-500);
  line-height: 18px;
  border-radius: 16px;
  padding: 1px 6px 1px 6px;
  background-color: var(--gray-200);
}

/* Responsive adjustments for estimated life section */
@media (max-width: 576px) {
  .estimated-life-section {
    gap: 2px;
  }

  .life-details {
    gap: 3px;
    flex-wrap: wrap;
  }

  .estimated-life-label {
    font-size: 11px;
  }

  .life-date,
  .life-duration {
    font-size: 11px;
  }
}

/* Loading State Styles */
.status-card-loading {
  background: var(--gray-25);
  border: 1px solid var(--gray-e1);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.loading-icon {
  background: var(--gray-100);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--gray-300);
  border-top: 2px solid var(--brand-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 12px;
  font-weight: 400;
  color: var(--gray-500);
  line-height: 18px;
}

/* Loading state header styling */
.current-condition h3 {
  transition: color 0.3s ease;
}

.current-condition h3:has-text('Calculating') {
  color: var(--gray-600);
}

.row-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--Gray-600, #475467);
  /* font-family: Diodrum; */
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 142.857% */
}

/* Parameter Row Styles */
.parameter-row {
  display: grid;
  grid-template-columns: 100px repeat(5, minmax(80px, 1fr));
  gap: 8px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--gray-f0);
  min-width: 600px;
  position: relative;
  margin-bottom: 24px; /* Increased to accommodate error messages */
}

@media (min-width: 768px) {
  .parameter-row {
    grid-template-columns: 120px repeat(5, 1fr);
    gap: 16px;
    padding: 12px 0;
    min-width: auto;
  }
}

.parameter-label {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: left;
}

/* Parameter unit styles moved to main parameter section */

.parameter-name {
  font-size: 12px;
  color: var(--gray-900);
  font-weight: 600;
  line-height: 14px;
}

@media (min-width: 768px) {
  .parameter-name {
    font-size: 13px;
    line-height: 16px;
  }
}

.parameter-inputs {
  display: contents;
}

.parameter-input {
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--gray-e1);
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  color: var(--gray-900);
  background: var(--white);
  outline: none;
  transition: border-color 0.2s;
}

@media (min-width: 768px) {
  .parameter-input {
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
  }

  /* Parameter Table Responsive Styles */
  .parameter-table-row {
    padding: 12px 0;
    min-width: auto;
  }

  .parameter-unit-text {
    font-size: 11px;
    line-height: 14px;
  }

  .parameter-name-text {
    font-size: 13px;
    line-height: 16px;
  }

  .parameter-table-input {
    /* height: 36px !important; */
  }

  .parameter-table-input .ant-input-number-input {
    height: 34px;
    padding: 0 12px;
    font-size: 14px;
  }

  .parameter-table-input .ant-input-number-group-addon {
    font-size: 12px;
    padding: 0 12px;
    min-width: 50px;
  }
}

.parameter-input:focus {
  border-color: var(--brand-500);
}

.parameter-input.normalized {
  background: var(--gray-f8);
  color: var(--gray-600);
}

.parameter-input.threshold {
  background: var(--gray-25);
}

.parameter-input:disabled {
  background: var(--gray-f8);
  color: var(--gray-500);
  cursor: not-allowed;
}

.parameter-table-input .ant-input-number.ant-input-number-disabled {
  /* Your styles here */
  border: none !important;
}

/* CIP Section Styles - Updated to match other form sections */
.cip-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cip-frequency-container,
.cip-duration-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cip-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--gray-500);
  line-height: 16px;
}

/* CIP section now uses standard NumericInput components, so custom styles are removed */

/* Current Condition Section */
.current-condition {
  background: var(--gray-f8);
  border: 1px solid var(--gray-e1);
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
  width: 100%;
  box-sizing: border-box;
}

@media (min-width: 768px) {
  .current-condition {
    padding: 24px;
    margin-top: 32px;
  }
}

.current-condition h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--brand-500);
  margin: 0 0 16px 0;
  line-height: 24px;
}

@media (min-width: 768px) {
  .current-condition h3 {
    font-size: 18px;
    margin: 0 0 24px 0;
    line-height: 28px;
  }
}

/* Additional responsive adjustments for very small screens */
@media (max-width: 480px) {
  .current-condition {
    padding: 12px;
    margin-top: 16px;
  }

  .current-condition h3 {
    font-size: 15px;
    margin: 0 0 12px 0;
    line-height: 22px;
  }
}

.condition-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .condition-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

.condition-info-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  background: var(--gray-25);
  border-radius: 6px;
  border: 1px solid var(--gray-e1);
}

.condition-info-icon {
  color: var(--gray-400);
  font-size: 16px;
  line-height: 20px;
  flex-shrink: 0;
  margin-top: 1px;
}

.condition-info-text {
  font-size: 12px;
  font-weight: 400;
  color: var(--gray-500);
  line-height: 18px;
}

.calculate-button {
  width: auto;
  height: 42px;

  background: var(--brand-500);
  border: none;
  border-radius: 26px;
  font-size: 16px;
  padding: 0 24px;
  color: var(--white);
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin: 0 auto;
  display: block;
}

/* Additional responsive styles consolidated */
@media (min-width: 768px) {
  #root {
    padding: 24px;
  }
  .ro-advisor {
    padding: 24px;
    min-height: calc(100vh - 48px);
  }
  .header {
    margin-bottom: 32px;
  }
  .title {
    font-size: 24px;
    line-height: 32px;
  }
  .settings-section {
    flex-direction: row;
    gap: 48px;
    margin-bottom: 32px;
  }
  .setting-group {
    width: auto;
  }
  .button-group-item {
    padding: 8px 16px;
    font-size: 14px;
    min-width: 80px;
  }
  .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .parameters-grid {
    margin-bottom: 32px;
    overflow-x: visible;
  }
  .grid-header {
    grid-template-columns: 120px repeat(5, 1fr);
    gap: 16px;
    padding: 16px 0;
    font-size: 14px;
    min-width: auto;
  }
  .parameter-row {
    grid-template-columns: 120px repeat(5, 1fr);
    gap: 16px;
    padding: 12px 0;
    min-width: auto;
  }
  .parameter-unit {
    font-size: 11px;
    line-height: 14px;
  }
  .parameter-name {
    font-size: 13px;
    line-height: 16px;
  }
  .parameter-input {
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
  }
  .input-label {
    font-size: 12px;
    line-height: 16px;
  }
  .input-field-input {
    font-size: 14px;
    line-height: 20px;
  }
  .input-unit,
  .input-action {
    padding: 0 12px;
    font-size: 14px;
    min-width: 60px;
  }
  .simple-unit {
    font-size: 14px;
    min-width: 60px;
  }
  .input-field {
    width: 200px;
  }
  .action-icon {
    width: 16px;
    height: 16px;
  }
}

@media (min-width: 1024px) {
  .data-grid {
    grid-template-columns: repeat(4, 1fr);
    margin-bottom: 32px;
  }
}

@media (min-width: 1200px) {
  #root {
    padding: 32px 48px;
  }
  .ro-advisor {
    padding: 32px;
    min-height: calc(100vh - 64px);
  }
}

@media (min-width: 1600px) {
  #root {
    padding: 48px 64px;
  }
  .ro-advisor {
    min-height: calc(100vh - 96px);
  }
}

/* Mobile-first responsive improvements */
@media (max-width: 767px) {
  /* .settings-row {
    flex-direction: column;
    gap: var(--spacing-md);
  } */

  .data-input-grid {
    flex-direction: column;
  }

  .data-column {
    margin-bottom: var(--spacing-md);
    position: relative;
    min-height: unset;
  }

  .parameters-table {
    overflow-x: auto;
    margin: var(--spacing-lg) -16px;
    padding: 0 16px;
  }

  .table-header,
  .table-row {
    min-width: 600px;
  }

  /* Parameter Table Mobile Styles */
  .parameter-table-container {
    overflow-x: auto;
    margin: var(--spacing-lg) -16px;
    padding: 0 16px;
  }

  .parameter-table-header,
  .parameter-table-row {
    min-width: 600px;
  }

  .calculate-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (min-width: 768px) {
  .data-column {
    position: relative;
    min-height: 350px;
  }
}

/* Help Text and Error Message Styles */
.input-wrapper {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.help-text-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  margin-top: 4px;
  min-height: 16px;
}

.help-text {
  font-size: 11px;
  color: #666;
  margin: 0;
  line-height: 1.2;
}

.error-text {
  font-size: 11px;
  color: #ff4d4f;
  margin: 0;
  line-height: 1.2;
}

.input-wrapper.has-error .ant-input-number {
  border-color: #ff4d4f;
}

.input-wrapper.has-error .ant-input-number:hover {
  border-color: #ff7875;
}

.input-wrapper.has-error .ant-input-number:focus,
.input-wrapper.has-error .ant-input-number-focused {
  border-color: #ff7875;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* Reduce spacing between parameter rows for more compact layout */
.parameter-table-row {
  margin-bottom: 16px;
  padding-bottom: 4px;
}

/* Ensure Form.Item help text and error messages are visible in parameter table */
.parameter-table-row .ant-form-item {
  margin-bottom: 0;
}

/* Reduce spacing for NumericInput components in forms */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-vertical .ant-form-item {
  /* margin-bottom: 12px; */
}

.parameter-table-row .ant-form-item-explain,
.parameter-table-row .ant-form-item-explain-error {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 2px;
  font-size: 10px;
  line-height: 12px;
  z-index: 10;
}

.parameter-table-row .ant-form-item-explain {
  color: var(--gray-500);
}

.parameter-table-row .ant-form-item-explain-error {
  color: #ff4d4f;
}

.input-field-container {
  position: relative;
  width: 100%;
}

.error-message {
  position: absolute;
  /* left: 9%;
  margin-top: 2px;
  */
  /* top: 9%; */
  padding: 4px 0 0 0;
  font-size: 10px;
  color: #ff4d4f;
  z-index: 2;
}

.input-error .ant-input-number {
  border-color: #ff4d4f !important;
}

.input-error .ant-input-number:hover {
  border-color: #ff7875 !important;
}

.input-error .ant-input-number:focus,
.input-error .ant-input-number-focused {
  border-color: #ff7875 !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

/* Increase spacing between rows to accommodate error messages */

/* Normalized input styling - override error styles */
.parameter-table-input-normalized {
  border: 1px solid var(--blue-border) !important;
  box-shadow: none !important;
  border-radius: 4px !important;
}

.parameter-table-input-normalized.input-error {
  border: 1px solid var(--blue-border) !important;
  box-shadow: none !important;
}

.parameter-table-input-normalized .ant-input-number {
  background: var(--gray-f8);
  border: none !important;
  box-shadow: none !important;
}

.parameter-table-input-normalized .ant-input-number-group-addon {
  border: none !important;
  background: var(--gray-f8);
  color: var(--gray-600);
}

/* Ensure these styles have higher specificity */
.parameter-table-input-normalized.input-error .ant-input-number,
.parameter-table-input-normalized.input-error .ant-input-number-group-addon {
  border-color: var(--blue-border) !important;
  box-shadow: none !important;
}

/* Normalized inputs should keep blue border even when focused with errors */
.ant-form-item-has-error .parameter-table-input-normalized:focus-within {
  border-color: var(--blue-border) !important;
  box-shadow: none !important;
}

.ant-form-item-has-error
  .parameter-table-input-normalized:focus-within
  .ant-input-number,
.ant-form-item-has-error
  .parameter-table-input-normalized:focus-within
  .ant-input-number-group-addon {
  border-color: var(--blue-border) !important;
  box-shadow: none !important;
}




/* Custom Units Styles */
.units-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-units-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 0px;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  height: 32px;
  text-align: right;
  padding-left: 20px;
}

.edit-units-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.edit-icon {
  font-size: 16px;
  padding-left: 19px;
  color: var(--brand-500);
}

.edit-units-btn .selected {
  background: #ffffff;
}

/* Custom Units Modal Styles */
.custom-units-modal-wrapper .ant-modal-content {
  padding: 0;
  /* width: %; */
  border-radius: 12px;
  overflow: hidden;
}

.custom-units-modal-wrapper .ant-modal-header {
  padding: 20px 20px 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.custom-units-modal-wrapper .ant-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.custom-units-modal-wrapper .ant-modal-close {
  top: 20px;
  right: 20px;
}

.custom-units-modal-wrapper .ant-modal-close-x {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  color: #8c8c8c;
}

.custom-units-modal-wrapper .ant-modal-body {
  padding: 24px;
}

/* Custom Units Grid Layout */
.custom-units-grid {
  display: flex;

  flex-direction: column;
  gap: 10px;
}

.custom-unit-selector {
  width: 200px;
}

.custom-unit-row {
  margin-bottom: 0 !important;
  align-items: flex-start;
}

.custom-unit-label-col {
  /* padding-right: 2px; */
  flex: 1;
  display: flex;
  /* align-items: center; */
}

.custom-unit-label {
  font-size: 16px;
  font-weight: 500;
  color: #595959;
  line-height: 1.5;
  display: block;
  margin-top: 8px;
}

.custom-unit-options-col {
  padding-left: 0;
  display: flex;
  justify-content: flex-end;
}
.custom-unit-header-row {
  margin-left: 60px;
}

.custom-unit-headers {
  display: flex;
  justify-content: space-between;
  padding: 0px 30px 0px 90px;
  /* Adjust this value to align with buttons */
}

.unit-header {
  font-weight: 500;
  color: #666;
}

.unit-header.us {
  margin-right: auto; /* Pushes US to align with left column of buttons */
}

.unit-header.metric {
  margin-left: auto; /* Pushes Metric to align with right column of buttons */
}

.custom-unit-buttons-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  width: 100%;
  max-width: 200px;
  margin-top: 5px;
}

.custom-unit-button {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 18px;
  background: var(--gray-e1);
  color: #595959;
  font-size: 0.8rem;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  text-align: center;
  white-space: nowrap;
}

.custom-unit-button.selected {
  background: #007672 !important;
  /* border-color: var(--primary-500); */
  color: #ffffff;
  font-weight: 500;
}

.custom-unit-error {
  font-size: 12px;
  color: var(--error-500);
  margin-top: 8px;
}

/* Custom Units Footer */
.custom-units-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  font-size: 16px;

  margin-top: 18px;
  border-top: 1px solid #f0f0f0;
}

.save-units-button {
  background: var(--brand-500);

  border-radius: 24px;
  padding: 8px 32px;
  height: 42px;
  font-size: 16px;
  font-weight: 500;
  min-width: 140px;
  color: #ffffff;
}

.save-units-button:hover,
.save-units-button:focus {
  background: var(--gray-600) !important;
  color: #ffffff;
}

.error-alert {
  margin-bottom: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
