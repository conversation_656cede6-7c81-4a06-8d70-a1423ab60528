/**
 * Validation utilities for form inputs and data
 */

/**
 * Validate if a value is a valid number
 * @param {string|number} value - The value to validate
 * @returns {boolean} True if valid number
 */
export const isValidNumber = (value) => {
  if (value === '' || value === null || value === undefined) return true; // Allow empty values
  const num = parseFloat(value);
  return !isNaN(num) && isFinite(num);
};

/**
 * Validate if a value is within a range
 * @param {string|number} value - The value to validate
 * @param {number} min - Minimum value (optional)
 * @param {number} max - Maximum value (optional)
 * @returns {boolean} True if within range
 */
export const isWithinRange = (value, min, max) => {
  if (!isValidNumber(value)) return false;
  const num = parseFloat(value);
  
  if (min !== undefined && num < min) return false;
  if (max !== undefined && num > max) return false;
  
  return true;
};

/**
 * Validate date format (basic validation)
 * @param {string} dateString - The date string to validate
 * @returns {boolean} True if valid date format
 */
export const isValidDate = (dateString) => {
  if (!dateString) return true; // Allow empty dates
  
  // Basic date format validation (MM/DD/YYYY or similar)
  const dateRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
  if (!dateRegex.test(dateString)) return false;
  
  // Try to parse the date
  const [month, day, year] = dateString.split('/').map(Number);
  const date = new Date(year, month - 1, day);
  
  return date.getFullYear() === year &&
         date.getMonth() === month - 1 &&
         date.getDate() === day;
};

/**
 * Validate required field
 * @param {any} value - The value to validate
 * @returns {boolean} True if not empty
 */
export const isRequired = (value) => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim().length > 0;
  return true;
};

/**
 * Validate email format
 * @param {string} email - The email to validate
 * @returns {boolean} True if valid email format
 */
export const isValidEmail = (email) => {
  if (!email) return true; // Allow empty emails
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Sanitize numeric input
 * @param {string} value - The input value
 * @returns {string} Sanitized value
 */
export const sanitizeNumericInput = (value) => {
  if (!value) return '';
  // Remove any non-numeric characters except decimal point and minus sign
  return value.replace(/[^0-9.-]/g, '');
};

/**
 * Format number for display
 * @param {string|number} value - The value to format
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted number
 */
export const formatNumber = (value, decimals = 2) => {
  if (!isValidNumber(value)) return value;
  const num = parseFloat(value);
  return num.toFixed(decimals);
};

/**
 * Validate form data completeness
 * @param {Object} formData - The form data to validate
 * @param {Array} requiredFields - Array of required field paths
 * @returns {Object} Validation result with isValid and errors
 */
export const validateFormData = (formData, requiredFields = []) => {
  const errors = [];
  
  requiredFields.forEach(fieldPath => {
    const value = getNestedValue(formData, fieldPath);
    if (!isRequired(value)) {
      errors.push(`${fieldPath} is required`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get nested value from object using dot notation
 * @param {Object} obj - The object to search
 * @param {string} path - The path (e.g., 'start.temperature')
 * @returns {any} The nested value
 */
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};
