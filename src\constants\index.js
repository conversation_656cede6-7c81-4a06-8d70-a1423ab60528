// Application Constants

// Membrane Types
export const MEMBRANE_TYPES = {
  RO: 'RO',
  NF: 'NF'
};

export const MEMBRANE_OPTIONS = [
  { value: MEMBRANE_TYPES.RO, label: 'Reverse Osmosis' },
  { value: MEMBRANE_TYPES.NF, label: 'Nanofiltration' }
];

// Unit Types
export const UNIT_TYPES = {
  US: 'US',
  METRIC: 'Metric',
  CUSTOM: 'Custom'
};

export const UNIT_OPTIONS = [
  { value: UNIT_TYPES.US, label: 'US' },
  { value: UNIT_TYPES.METRIC, label: 'Metric' },
  { value: UNIT_TYPES.CUSTOM, label: 'Custom' }
];

// Status Types
export const STATUS_TYPES = {
  NORMAL: 'normal',
  WARNING: 'warning',
  ERROR: 'error'
};

// Icon Types
export const ICON_TYPES = {
  TEMPERATURE: 'temperature',
  FLOW: 'flow',
  CONDUCTIVITY: 'conductivity',
  DEFAULT: 'default'
};

// Parameter Types
export const PARAMETER_TYPES = {
  FEED_PRESSURE: 'feedPressure',
  DP: 'dP',
  Q_PERM: 'qPerm',
  COND_PERM: 'condPerm'
};

// Form Field Types
export const FORM_SECTIONS = {
  START: 'start',
  CURRENT: 'current',
  FORECAST: 'forecast',
  CIP: 'cip'
};

// Default Values
export const DEFAULT_VALUES = {
  CIP_FREQUENCY: 15,
  CIP_DURATION: '00000',
  // PLACEHOLDER_DATE: '01/22/2001'
};

// CIP Configuration
export const CIP_CONFIG = {
  FREQUENCY_LABEL: 'CIP Frequency (#/year)',
  DURATION_LABEL: 'CIP Duration',
  FREQUENCY_MIN: 0,
  FREQUENCY_MAX: 100,
  DURATION_PLACEHOLDER: '00000'
};

// Units Configuration
export const UNITS_CONFIG = {
  temperature: 'ºC',
  feedFlow: 'm³/d',
  feedConductivity: 'μS/cm',
  pressure: 'bar',
  duration: 'days',
  cipFrequency: 'year',
  cipDuration: 'days'
};

// Status Colors
//FFFBCC
export const STATUS_COLORS = {
  [STATUS_TYPES.NORMAL]: '#10B981',
  [STATUS_TYPES.WARNING]: '#F79009',
  [STATUS_TYPES.ERROR]: '#F04438',
  default: 'var(--gray-500)'
};

// Status Text
export const STATUS_TEXT = {
  [STATUS_TYPES.NORMAL]: 'Normal Operation',
  [STATUS_TYPES.WARNING]: 'Needs Monitoring',
  [STATUS_TYPES.ERROR]: 'Critical',
  default: 'Unknown'
};

// Status Icons
export const STATUS_ICONS = {
  [STATUS_TYPES.NORMAL]: '✓',
  [STATUS_TYPES.WARNING]: '⚠',
  [STATUS_TYPES.ERROR]: '✕',
  default: '●'
};
