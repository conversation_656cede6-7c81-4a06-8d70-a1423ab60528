import { Typography } from 'antd';
import { useFormActions } from '../../contexts';
import NumericInput from '../NumericInput';

const { Text } = Typography;

/**
 * Parameter Row Component
 * Displays a single parameter row with all fields using form context
 * No longer receives parameter data as props - gets from context
 */
export default function ParameterRow({
  label,
  parameterType,
  normalizedDisabled = true,
  thresholdDisabled = false,
}) {
  const { updateParameter } = useFormActions();

  // Handle parameter field changes
  const handleParameterChange = (field, value) => {
    updateParameter(parameterType, field, value);
  };

  // No longer need custom ParameterInput - using NumericInput with Form.Item validation

  return (
    <div className="parameter-table-row" style={{ marginBottom: '24px' }}>
      <div style={{ flex: '0 0 100px' }}>
        <Text className="parameter-name-text">{label}</Text>
      </div>

      <div style={{ flex: 1, position: 'relative' }}>
        <NumericInput
          name={['parameters', parameterType, 'start']}
          parameterType={parameterType}
          fieldName="start"
          onChange={value => handleParameterChange('start', value)}
          // className="parameter-table-input"
          required
          style={{ marginBottom: 0 }}
        />
      </div>

      <div style={{ flex: 1, position: 'relative' }}>
        <NumericInput
          name={['parameters', parameterType, 'current']}
          parameterType={parameterType}
          fieldName="current"
          onChange={value => handleParameterChange('current', value)}
          // className="parameter-table-input"
          required
          style={{ marginBottom: 0 }}
        />
      </div>

      <div style={{ flex: 1, position: 'relative' }}>
        <NumericInput
          name={['parameters', parameterType, 'normalized']}
          parameterType={parameterType}
          fieldName="normalized"
          onChange={value => handleParameterChange('normalized', value)}
          isReadOnly
          disabled={normalizedDisabled}
          className="parameter-table-input parameter-table-input-normalized"
          style={{ marginBottom: 0 }}
        />
      </div>

      <div style={{ flex: 1, position: 'relative' }}>
        <NumericInput
          name={['parameters', parameterType, 'limit']}
          parameterType={parameterType}
          fieldName="limit"
          onChange={value => handleParameterChange('limit', value)}
          // className="parameter-table-input"
          required
          style={{ marginBottom: 0 }}
        />
      </div>

      <div style={{ flex: 1, position: 'relative' }}>
        <NumericInput
          name={['parameters', parameterType, 'threshold']}
          parameterType={parameterType}
          fieldName="threshold"
          onChange={value => handleParameterChange('threshold', value)}
          isReadOnly
          disabled={thresholdDisabled}
          className="parameter-table-input parameter-table-input-normalized"
          style={{ marginBottom: 0 }}
        />
      </div>
    </div>
  );
}
