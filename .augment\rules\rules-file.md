---
type: 'agent_requested'
description: 'rules'
---

# RoAdvisorLite Development Rules and Guidelines

## Table of Contents

- [Project Overview](#project-overview)
- [Technology Stack](#technology-stack)
- [Code Quality Standards](#code-quality-standards)
- [Component Development Guidelines](#component-development-guidelines)
- [State Management Rules](#state-management-rules)
- [Styling Standards](#styling-standards)
- [Validation System Guidelines](#validation-system-guidelines)
- [Custom Units Integration](#custom-units-integration)
- [File Organization](#file-organization)
- [Performance Guidelines](#performance-guidelines)
- [Testing Requirements](#testing-requirements)
- [Git Workflow](#git-workflow)

## Project Overview

RoAdvisorLite is a React-based web application for Reverse Osmosis (RO) membrane performance analysis and forecasting. The application follows modern React patterns with centralized state management, comprehensive validation, and advanced custom unit support.

### Core Principles

- **Maintainability**: Code should be easy to read, understand, and modify
- **Consistency**: Follow established patterns throughout the codebase
- **Performance**: Optimize for efficient rendering and state updates
- **Accessibility**: Ensure all components are accessible to all users
- **Reusability**: Create components that can be reused across the application

## Technology Stack

### Required Dependencies

- **React**: 19.1.0+ (with hooks and modern patterns)
- **Ant Design**: 5.26.2+ (primary UI library)
- **Vite**: 6.3.5+ (build tool and dev server)
- **PropTypes**: 15.8.1+ (runtime type checking)

### Development Tools

- **ESLint**: Code linting with React-specific rules
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit validation
- **lint-staged**: Run linters on staged files only

## Code Quality Standards

### ESLint Configuration

All code must pass ESLint validation with the following rules:

- `react/prop-types`: 'warn' - PropTypes are required for all components
- `react/react-in-jsx-scope`: 'off' - Not needed in React 17+
- `no-unused-vars`: 'warn' - Remove unused variables
- `no-console`: 'warn' - Avoid console statements in production
- `prefer-const`: 'error' - Use const for non-reassigned variables
- `no-var`: 'error' - Use let/const instead of var

### Prettier Configuration

Code formatting is enforced with:

- Single quotes for strings
- Semicolons required
- 2-space indentation
- 80 character line width
- Trailing commas in ES5

### Pre-commit Hooks

All commits must pass:

1. ESLint validation with auto-fix
2. Prettier formatting
3. Staged file linting

## Component Development Guidelines

### Component Structure

All components must follow this structure:

```javascript
import { useState, useEffect, useMemo, useCallback } from 'react';
import { ComponentName } from 'antd';
import PropTypes from 'prop-types';

export default function MyComponent({ prop1, prop2, ...props }) {
  // 1. Hooks (useState, useEffect, custom hooks)
  // 2. Computed values (useMemo, useCallback)
  // 3. Event handlers
  // 4. Render logic

  return (
    // JSX content
  );
}

// PropTypes validation is REQUIRED
MyComponent.propTypes = {
  prop1: PropTypes.string.isRequired,
  prop2: PropTypes.func,
  // ... all props must be documented
};
```

### Component Naming

- Use PascalCase for component names
- File names should match component names
- Use descriptive, self-documenting names
- Avoid abbreviations unless widely understood

### PropTypes Requirements

- **MANDATORY**: All components must include comprehensive PropTypes
- Document all props with appropriate types
- Mark required props with `.isRequired`
- Use specific PropTypes (not just `PropTypes.any`)

### Ant Design Integration

- **ALWAYS** use Ant Design components when available
- Follow Ant Design design system guidelines
- Use Ant Design's grid system for layouts
- Leverage built-in Form.Item validation over custom solutions
- Maintain consistent component dimensions (200px width, 32px height for inputs)

### Accessibility Requirements

- Include ARIA labels for interactive elements
- Ensure keyboard navigation support
- Use semantic HTML elements
- Provide meaningful alt text for images
- Test with screen readers

## State Management Rules

### Form Context System

The application uses a three-layer context architecture:

```javascript
FormProvider
├── FormDataProvider (data state)
├── FormActionsProvider (actions & conversions)
└── FormValidationProvider (validation logic)
```

### Context Usage Rules

1. **Use form context for all form-related state** - Never use local state for form data
2. **Access contexts through custom hooks** - Use `useFormData()`, `useFormActions()`, `useFormValidation()`
3. **Avoid prop drilling** - Pass data through context, not props
4. **Optimize performance** - Use context splitting to prevent unnecessary re-renders

### Local State Guidelines

Reserve local state only for:

- Component-specific UI state (modals, dropdowns)
- Temporary input states before validation
- Animation states
- Loading states specific to the component

### Custom Hooks

Extract complex logic into custom hooks:

- Prefix with `use` (e.g., `useCustomUnits`, `useValidation`)
- Keep hooks focused on single responsibilities
- Include proper dependency arrays in useEffect/useMemo/useCallback
- Document hook parameters and return values

## Styling Standards

### CSS Variables

Use defined CSS variables for consistency:

```css
:root {
  --brand-500: #1890ff;
  --gray-500: #8c8c8c;
  --white: #ffffff;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --font-size-base: 14px;
}
```

### Component Dimensions

- **Input fields**: 200px width, 32px height
- **Consistent spacing**: Use CSS variables for margins/padding
- **Grid layouts**: Use Ant Design's grid system
- **Responsive breakpoints**: Follow Ant Design breakpoints

### Styling Approach

1. **Prefer Ant Design classes** over custom CSS
2. **Use CSS variables** for colors and spacing
3. **Avoid inline styles** except for dynamic values
4. **Create semantic CSS classes** for reusable styles
5. **Maintain responsive behavior** across all device sizes

## Validation System Guidelines

### Validation Architecture

The application uses a centralized validation system:

```javascript
// Parameter validation (for parameter table)
getParameterFieldValidation(parameterType, fieldName, unitSystem, customUnits);

// Form field validation (for form inputs)
getFormFieldValidation(formFieldType, unitSystem, customUnits);
```

### Validation Rules

1. **Field-level validation** with immediate feedback
2. **Unit-system aware** constraints that adapt to current units
3. **Custom unit support** with proper conversion
4. **Skip validation** for disabled/calculated fields
5. **Clear error messages** with specific limits and units

### Error Handling

- Replace help text with error messages (don't show both)
- Use Ant Design's Form.Item built-in validation
- Provide contextual error messages with units
- Validate on both onChange and onBlur events

## Custom Units Integration

### Requirements

All new components must support custom units:

1. **Access custom units** through form context
2. **Update unit labels** immediately when custom units change
3. **Convert values** properly when switching unit systems
4. **Update validation constraints** based on custom units
5. **Persist selections** across page reloads

### Implementation Pattern

```javascript
const { unitSystem, customUnits } = useFormData();

// Get dynamic unit label
const unitLabel = getUnitLabel(parameterType, unitSystem, customUnits);

// Use in Ant Design Input
<InputNumber addonAfter={unitLabel} />;
```

## File Organization

### Directory Structure

```
src/
├── components/          # Reusable UI components
├── contexts/           # React Context providers and hooks
├── hooks/              # Custom hooks
├── config/             # Configuration files
├── constants/          # Application constants
├── utils/              # Utility functions
│   ├── calculations/   # Calculation utilities
│   └── validation/     # Validation system
└── pages/              # Page components (if applicable)
```

### File Naming

- Use PascalCase for component files
- Use camelCase for utility files
- Use kebab-case for configuration files
- Include descriptive names that indicate purpose

### Import Organization

```javascript
// 1. React imports
import { useState, useEffect } from 'react';

// 2. Third-party imports
import { Form, Input } from 'antd';
import PropTypes from 'prop-types';

// 3. Internal imports (contexts, hooks, utils)
import { useFormData } from '../contexts/useFormContext';
import { validateInput } from '../utils/validation';

// 4. Relative imports
import './Component.css';
```

## Performance Guidelines

### React Performance

1. **Use useMemo** for expensive calculations
2. **Use useCallback** for event handlers passed to children
3. **Implement proper dependency arrays** in hooks
4. **Avoid creating objects/functions in render**
5. **Use context splitting** to prevent unnecessary re-renders

### Bundle Optimization

1. **Import only needed Ant Design components**
2. **Use dynamic imports** for large components
3. **Optimize images** and assets
4. **Remove unused dependencies**

## Testing Requirements

### Component Testing

- Write unit tests for all new components
- Test component props and behavior
- Test accessibility features
- Mock external dependencies

### Integration Testing

- Test form context integration
- Test custom units functionality
- Test validation behavior
- Test unit conversion accuracy

### Manual Testing Checklist

- [ ] Component renders correctly
- [ ] PropTypes validation works
- [ ] Accessibility features function
- [ ] Responsive design works
- [ ] Custom units integration works
- [ ] Validation provides appropriate feedback

## Git Workflow

### Commit Standards

- Use conventional commit messages
- Keep commits focused and atomic
- Include descriptive commit messages
- Reference issue numbers when applicable

### Branch Naming

- Use descriptive branch names
- Include feature/bugfix prefix
- Use kebab-case for branch names

### Pre-commit Requirements

All commits must pass:

1. ESLint validation
2. Prettier formatting
3. Type checking (PropTypes)
4. Manual testing checklist

## Package Management Rules

### Dependency Management

- **ALWAYS use package managers** (npm, yarn, pnpm) for dependency management
- **NEVER manually edit** package.json, package-lock.json, or similar files
- Use appropriate commands for each action:
  - `npm install <package>` for new dependencies
  - `npm install --save-dev <package>` for dev dependencies
  - `npm uninstall <package>` to remove dependencies
  - `npm update` to update dependencies

### Version Management

- Pin major versions to avoid breaking changes
- Use semantic versioning (^x.y.z) for minor updates
- Test thoroughly after dependency updates
- Document any breaking changes in migration guides

## Unit Conversion System

### Conversion Factors

The application uses specific conversion factors that must be maintained:

```javascript
CONVERSION_FACTORS = {
  PRESSURE: { bar_to_psi: 14.5038 },
  FLOW: {
    m3h_to_gpm: 4.4029,
    m3h_to_m3d: 24,
    gpm_to_gpd: 1440,
  },
  CONDUCTIVITY: {
    uScm_to_ppm: 0.5,
    uScm_to_mgL: 0.5,
  },
  TEMPERATURE: {
    celsius_to_fahrenheit: c => (c * 9) / 5 + 32,
  },
};
```

### Conversion Implementation

1. **Use centralized conversion functions** from `utils/unitConversion.js`
2. **Support bidirectional conversion** between all unit systems
3. **Maintain precision** in calculations
4. **Handle edge cases** (null, undefined, invalid values)
5. **Test conversion accuracy** with known values

## Data Flow Patterns

### Input Processing Flow

```
User Input → NumericInput → Form Context → Validation → State Update → UI Refresh
```

### Calculation Flow

```
Form Data → Parameter Calculations → Normalized Values → Status Determination → Display Update
```

### Unit Conversion Flow

```
Unit Change → Form Context → Convert Form Data → Convert Parameters → Update Labels → UI Refresh
```

### Custom Units Flow

```
Custom Modal → Save Units → Update Context → Convert Values → Refresh Labels → Persist to Storage
```

## Error Handling Standards

### Error Boundaries

- Implement error boundaries for major sections
- Provide fallback UI for component failures
- Log errors for debugging purposes
- Display user-friendly error messages

### Validation Errors

- Show errors immediately on invalid input
- Replace help text with error messages
- Use specific error messages with units
- Clear errors when input becomes valid

### Network Errors

- Handle API failures gracefully
- Provide retry mechanisms
- Show loading states during requests
- Cache data when appropriate

## Documentation Requirements

### Code Documentation

- **JSDoc comments** for all functions and complex logic
- **Inline comments** for non-obvious code sections
- **README updates** for new features
- **Migration guides** for breaking changes

### Component Documentation

```javascript
/**
 * NumericInput Component
 *
 * A reusable numeric input with validation and unit support
 *
 * @param {string} name - Input name for form handling
 * @param {string} label - Display label for the input
 * @param {function} onChange - Callback when value changes
 * @param {string} unit - Unit label to display
 * @param {boolean} disabled - Whether input is disabled
 * @param {string} parameterType - Type for validation (pressure, flow, etc.)
 * @param {string} fieldName - Field name for validation rules
 *
 * @example
 * <NumericInput
 *   name="temperature"
 *   label="Temperature"
 *   onChange={handleChange}
 *   unit="°C"
 *   parameterType="temperature"
 *   fieldName="startTemperature"
 * />
 */
```

## Security Guidelines

### Input Validation

- Validate all user inputs on both client and server
- Sanitize inputs to prevent XSS attacks
- Use parameterized queries for database operations
- Implement rate limiting for API endpoints

### Data Protection

- Never store sensitive data in localStorage
- Use HTTPS for all communications
- Implement proper authentication/authorization
- Follow OWASP security guidelines

## Deployment Guidelines

### Build Process

```bash
# Development
npm run dev

# Production build
npm run build

# Preview production build
npm run preview

# Linting and formatting
npm run lint
npm run format
```

### Environment Configuration

- Use environment variables for configuration
- Separate development and production configs
- Never commit sensitive information
- Document all required environment variables

### Performance Monitoring

- Monitor bundle size and loading times
- Use React DevTools for performance profiling
- Implement error tracking in production
- Monitor user experience metrics

## Troubleshooting Guide

### Common Issues

#### ESLint Errors

- **Issue**: "Unknown command: lint-staged"
- **Solution**: Use `npx lint-staged` in pre-commit hooks

#### React Warnings

- **Issue**: Missing PropTypes warnings
- **Solution**: Add comprehensive PropTypes to all components

#### Performance Issues

- **Issue**: Slow re-renders
- **Solution**: Check context usage and implement proper memoization

#### Unit Conversion Errors

- **Issue**: Incorrect conversion values
- **Solution**: Verify conversion factors and test with known values

### Debugging Tools

- React Developer Tools
- ESLint output
- Browser DevTools
- Network tab for API issues
- Console for JavaScript errors

## Migration Patterns

### Component Migration

When updating existing components:

1. **Maintain backward compatibility** when possible
2. **Create migration guides** for breaking changes
3. **Update tests** to reflect new behavior
4. **Document changes** in commit messages

### Validation Migration

When updating validation:

1. **Preserve existing validation behavior**
2. **Add new validation rules incrementally**
3. **Test edge cases** thoroughly
4. **Update error messages** to be more helpful

## Best Practices Summary

### DO

✅ Use Ant Design components whenever possible
✅ Include comprehensive PropTypes for all components
✅ Use form context for all form-related state
✅ Implement proper accessibility features
✅ Write unit tests for new components
✅ Follow established naming conventions
✅ Use CSS variables for consistent styling
✅ Implement proper error handling
✅ Document complex logic with comments
✅ Use package managers for dependency management

### DON'T

❌ Create custom components when Ant Design alternatives exist
❌ Use local state for form data
❌ Skip PropTypes validation
❌ Ignore accessibility requirements
❌ Manually edit package configuration files
❌ Use inline styles for static values
❌ Create components without proper documentation
❌ Ignore ESLint warnings
❌ Skip testing for new features
❌ Use abbreviations in component names

---

_This document should be updated as the project evolves and new patterns emerge. Last updated: 2025-07-17_
