import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { ConfigProvider } from 'antd';
import antdTheme from './config/antdTheme.js';
import './index.css';
import App from './App.jsx';

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <ConfigProvider theme={{
      antdTheme,
      components:{
        DatePicker:{
         
          colorPrimary: '#667085',
        }
      }
    }

    }>
      <App />
    </ConfigProvider>
  </StrictMode>
);
