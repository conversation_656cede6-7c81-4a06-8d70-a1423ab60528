/**
 * Form Context Infrastructure
 * Core context definitions for the centralized form state management system
 */

import { createContext } from 'react';

/**
 * Form Data Context
 * Manages all form state including start, current, forecast sections and parameter data
 */
export const FormDataContext = createContext(null);

/**
 * Form Actions Context
 * Provides all form actions and state update functions
 */
export const FormActionsContext = createContext(null);

/**
 * Form Validation Context
 * Manages validation state and provides validation utilities
 */
export const FormValidationContext = createContext(null);

/**
 * Context Display Names for React DevTools
 */
FormDataContext.displayName = 'FormDataContext';
FormActionsContext.displayName = 'FormActionsContext';
FormValidationContext.displayName = 'FormValidationContext';

/**
 * Context Error Messages
 */
export const CONTEXT_ERROR_MESSAGES = {
  FORM_DATA_NOT_FOUND: 'useFormData must be used within a FormProvider',
  FORM_ACTIONS_NOT_FOUND: 'useFormActions must be used within a FormProvider',
  FORM_VALIDATION_NOT_FOUND: 'useFormValidation must be used within a FormProvider',
  FORM_CONTEXT_NOT_FOUND: 'useFormContext must be used within a FormProvider'
};

/**
 * Form Field Path Utilities
 * Helper functions for working with nested form field paths
 */
export const FormFieldPath = {
  /**
   * Create a field path for form data
   * @param {string} section - Form section (start, current, forecast)
   * @param {string} field - Field name
   * @returns {string} Field path
   */
  formField: (section, field) => `formData.${section}.${field}`,

  /**
   * Create a field path for parameter data
   * @param {string} parameterType - Parameter type
   * @param {string} field - Field name (start, current, normalized, limit, threshold)
   * @returns {string} Field path
   */
  parameterField: (parameterType, field) => `parameters.${parameterType}.${field}`,

  /**
   * Create a field path for CIP data
   * @param {string} field - Field name (frequency, duration)
   * @returns {string} Field path
   */
  cipField: (field) => `cipData.${field}`,

  /**
   * Parse a field path to extract components
   * @param {string} path - Field path
   * @returns {Object} Parsed path components
   */
  parse: (path) => {
    const parts = path.split('.');
    if (parts[0] === 'formData') {
      return { type: 'form', section: parts[1], field: parts[2] };
    } else if (parts[0] === 'parameters') {
      return { type: 'parameter', parameterType: parts[1], field: parts[2] };
    } else if (parts[0] === 'cipData') {
      return { type: 'cip', field: parts[1] };
    }
    return { type: 'unknown', path };
  }
};
