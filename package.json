{"name": "roadvisorlite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.6.0", "antd": "^5.26.2", "dayjs": "^1.11.13", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.4", "prettier": "^3.6.2", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}