/**
 * Validation hooks for React components
 * @module validation/hooks
 */

import { useState, useCallback, useMemo } from 'react';
import { validateWithRules, createValidationRule } from './validationCore';
import { validateParameterValue, shouldValidateParameterField,shouldShowParameterError} from './parameterValidation';

/**
 * Hook for parameter field validation with unit system support
 * @param {string} parameterType - Type of parameter (e.g. 'pressure', 'temperature')
 * @param {string} unitSystem - Unit system to use ('US', 'Metric', or 'Custom')
 * @param {Object} customUnits - Custom unit preferences (used when unitSystem is 'Custom')
 * @returns {Object} Validation utilities and state
 * @example
 * const { validateAndUpdate, shouldShowError, getError } = useParameterValidation('pressure', 'US');
 *
 * // Use in component:
 * const handleChange = (value) => {
 *   validateAndUpdate(value, onChange, 'current');
 * };
 */
export const useParameterValidation = (parameterType, unitSystem, customUnits = {}) => {
  const [errors, setErrors] = useState({});
  const [lastValidation, setLastValidation] = useState({});

  const validateAndUpdate = useCallback((value, onChange, fieldName, options = {}) => {
    // Skip validation for read-only/disabled fields
    if (!shouldValidateParameterField(fieldName, options.isDisabled, options.isReadOnly)) {
      onChange?.(value);
      return;
    }

    const result = validateParameterValue(value, parameterType, fieldName, unitSystem, customUnits);
    
    setLastValidation(prev => ({
      ...prev,
      [fieldName]: result
    }));

    if (result.isValid) {
      setErrors(prev => ({ ...prev, [fieldName]: '' }));
      onChange?.(result.value);
    } else {
      setErrors(prev => ({ ...prev, [fieldName]: result.error }));
    }

    return result;
  }, [parameterType, unitSystem]);

  /**
   * Clear error for a specific field
   * @param {string} fieldName - Field name
   */
  const clearError = useCallback((fieldName) => {
    setErrors(prev => ({ ...prev, [fieldName]: '' }));
  }, []);
  
  /**
   * Clear all errors
   */
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);
  
  /**
   * Check if a field should show error
   * @param {string} fieldName - Field name
   * @param {boolean} isDisabled - Whether field is disabled
   * @param {boolean} isReadOnly - Whether field is read-only
   * @returns {boolean} True if should show error
   */
  const shouldShowError = useCallback((fieldName, isDisabled = false, isReadOnly = false) => {
    return errors[fieldName] && shouldShowParameterError(fieldName, isDisabled, isReadOnly);
  }, [errors]);
  
  /**
   * Get error message for a field
   * @param {string} fieldName - Field name
   * @returns {string} Error message
   */
  const getError = useCallback((fieldName) => {
    return errors[fieldName] || '';
  }, [errors]);
  
  /**
   * Check if any fields have errors
   * @returns {boolean} True if there are errors
   */
  const hasErrors = useMemo(() => {
    return Object.values(errors).some(error => error && error.length > 0);
  }, [errors]);
  
  return {
    errors,
    validateAndUpdate,
    clearError,
    clearAllErrors,
    shouldShowError,
    getError,
    hasErrors
  };
};

/**
 * Hook for numeric input validation with constraints
 * @param {Object} constraints - Validation constraints
 * @param {number} [constraints.min] - Minimum value
 * @param {number} [constraints.max] - Maximum value
 * @param {string} [constraints.unit] - Unit for error messages
 * @returns {Object} Numeric validation utilities
 * @example
 * const { validate, validateAndUpdate, error } = useNumericValidation({
 *   min: 0,
 *   max: 100,
 *   unit: '°C'
 * });
 */
export const useNumericValidation = (constraints = {}) => {
  const [error, setError] = useState('');
  const [lastValue, setLastValue] = useState(null);

  console.log('useNumericValidation constraints:', constraints);
  const rules = useMemo(() => {
    const validationRules = [createValidationRule('number')];
    if (constraints.min !== undefined || constraints.max !== undefined) {
      validationRules.push(createValidationRule('range', constraints));
    }
    if (constraints.required) {
      validationRules.unshift(createValidationRule('required'));
    }
    return validationRules;
  }, [constraints]);

  const validate = useCallback((value) => {
    const result = validateWithRules(value, rules);
    setError(result.error);
    setLastValue(result.value);
    return result;
  }, [rules]);

  const validateAndUpdate = useCallback((value, onChange) => {
    const result = validate(value);
    if (result.isValid) {
      onChange?.(result.value);
    }
    return result;
  }, [validate]);

  return {
    error,
    validate,
    validateAndUpdate,
    lastValue,
    clearError: () => setError('')
  };
};

/**
 * Hook for date input validation
 * @param {Object} options - Validation options
 * @returns {Object} Date validation utilities
 */
export const useDateValidation = (options = {}) => {
  const [error, setError] = useState('');
  
  /**
   * Validate date value
   * @param {string} value - Date string to validate
   * @returns {ValidationResult} Validation result
   */
  const validate = useCallback((value) => {
    const { validateDateInput } = require('./formValidation');
    const result = validateDateInput(value, options);
    
    setError(result.error);
    
    return result;
  }, [options]);
  
  /**
   * Validate and update value
   * @param {string} value - Date string to validate
   * @param {Function} onChange - Change handler
   */
  const validateAndUpdate = useCallback((value, onChange) => {
    const result = validate(value);
    onChange?.(value);
    return result;
  }, [validate]);
  
  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setError('');
  }, []);
  
  return {
    error,
    validate,
    validateAndUpdate,
    clearError,
    hasError: !!error
  };
};

/**
 * Hook for field-level validation with custom validator
 * @param {Function} validator - Custom validation function
 * @returns {Object} Custom validation utilities
 */
export const useCustomValidation = (validator) => {
  const [error, setError] = useState('');
  
  /**
   * Validate value with custom validator
   * @param {any} value - Value to validate
   * @returns {ValidationResult} Validation result
   */
  const validate = useCallback((value) => {
    if (!validator) {
      return { isValid: true, error: '', value };
    }
    
    const result = validator(value);
    setError(result.error || '');
    
    return result;
  }, [validator]);
  
  /**
   * Validate and update value
   * @param {any} value - Value to validate
   * @param {Function} onChange - Change handler
   */
  const validateAndUpdate = useCallback((value, onChange) => {
    const result = validate(value);
    onChange?.(value);
    return result;
  }, [validate]);
  
  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setError('');
  }, []);
  
  return {
    error,
    validate,
    validateAndUpdate,
    clearError,
    hasError: !!error
  };
};
