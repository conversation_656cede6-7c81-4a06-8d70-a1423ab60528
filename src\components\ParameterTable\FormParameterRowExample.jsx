import { Typography } from 'antd';
import { useMemo } from 'react';
import { calculateNormalizedValue } from '../../utils/calculations/normalizedCalculations';
import { getParameterConfigForUnit } from '../../config/parameterConfig';
import { getUnitLabel } from '../../utils/unitConversion';
import { useFormData, useFormActions } from '../../contexts';
import NumericInput from '../NumericInput'; // Updated NumericInput with Form.Item

const { Text } = Typography;

/**
 * Updated Parameter Row Component using Form.Item validation
 * This shows how to migrate from the old validation approach to the new one
 */
export default function FormParameterRowExample({
  label,
  unit,
  parameterType,
  normalizedDisabled = true,
  thresholdDisabled = false,
}) {
  // Get form data from context
  const { parameters, formData, unitSystem, customUnits } = useFormData();
  const { updateParameter } = useFormActions();

  // Get parameter data from context
  const parameterData = parameters[parameterType] || {};
  const {
    start: startValue,
    current: currentValue,
    normalized: normalizedValue,
    limit: limitValue,
    threshold: thresholdValue,
  } = parameterData;

  // Calculate normalized value
  const calculatedNormalizedValue = useMemo(
    () =>
      calculateNormalizedValue(
        parameterType,
        startValue,
        currentValue,
        formData,
        parameters,
        normalizedValue
      ),
    [parameterType, startValue, currentValue, formData, parameters, normalizedValue]
  );

  // Get current unit label
  const currentUnitLabel = useMemo(() => {
    const config = getParameterConfigForUnit(
      parameterType,
      unitSystem,
      customUnits
    );
    if (config && config.parameterType) {
      return getUnitLabel(config.parameterType, unitSystem, customUnits);
    }
    return unit; // Fallback to original unit
  }, [parameterType, unitSystem, customUnits, unit]);

  // Handle parameter field changes
  const handleParameterChange = (field, value) => {
    updateParameter(parameterType, field, value);
  };

  return (
    <div className="parameter-table-row">
      <div style={{ flex: '0 0 100px' }}>
        <Text className="parameter-name-text">{label}</Text>
      </div>
      
      {/* Start Value - Uses dynamic validation */}
      <div style={{ flex: 1 }}>
        <NumericInput
          name={['parameters', parameterType, 'start']}
          parameterType={parameterType}
          fieldName="start"
          onChange={(value) => handleParameterChange('start', value)}
          className="parameter-table-input"
        />
      </div>
      
      {/* Current Value - Uses dynamic validation */}
      <div style={{ flex: 1 }}>
        <NumericInput
          name={['parameters', parameterType, 'current']}
          parameterType={parameterType}
          fieldName="current"
          onChange={(value) => handleParameterChange('current', value)}
          className="parameter-table-input"
        />
      </div>
      
      {/* Normalized Value - Read-only, no validation needed */}
      <div style={{ flex: 1 }}>
        <NumericInput
          name={['parameters', parameterType, 'normalized']}
          parameterType={parameterType}
          fieldName="normalized"
          onChange={(value) => handleParameterChange('normalized', value)}
          isReadOnly={normalizedDisabled}
          disabled={normalizedDisabled}
          className="parameter-table-input parameter-table-input-disabled"
        />
      </div>
      
      {/* Limit Value - Uses dynamic validation */}
      <div style={{ flex: 1 }}>
        <NumericInput
          name={['parameters', parameterType, 'limit']}
          parameterType={parameterType}
          fieldName="limit"
          onChange={(value) => handleParameterChange('limit', value)}
          className="parameter-table-input"
        />
      </div>
      
      {/* Threshold Value - Read-only or editable based on props */}
      <div style={{ flex: 1 }}>
        <NumericInput
          name={['parameters', parameterType, 'threshold']}
          parameterType={parameterType}
          fieldName="threshold"
          onChange={(value) => handleParameterChange('threshold', value)}
          isReadOnly={thresholdDisabled}
          disabled={thresholdDisabled}
          className="parameter-table-input parameter-table-input-disabled"
        />
      </div>
    </div>
  );
}

/**
 * Migration Guide:
 * 
 * OLD APPROACH (Complex):
 * ```jsx
 * const { validateAndUpdate, shouldShowError, getError } = useParameterValidation(
 *   parameterType, unitSystem, customUnits
 * );
 * 
 * const ParameterInput = ({ value, fieldName, isReadOnly, isDisabled }) => {
 *   const error = shouldShowError(fieldName, isDisabled, isReadOnly) ? getError(fieldName) : '';
 *   
 *   const handleChange = (newValue) => {
 *     validateAndUpdate(newValue, (validatedValue) => {
 *       handleParameterChange(fieldName, validatedValue);
 *     }, fieldName, { isDisabled, isReadOnly });
 *   };
 *   
 *   return (
 *     <div>
 *       <InputNumber
 *         value={value}
 *         onChange={handleChange}
 *         status={error ? 'error' : ''}
 *         addonAfter={currentUnitLabel}
 *         readOnly={isReadOnly}
 *         disabled={isDisabled}
 *       />
 *       {error && <div className="error-message">{error}</div>}
 *     </div>
 *   );
 * };
 * ```
 * 
 * NEW APPROACH (Simple):
 * ```jsx
 * <NumericInput
 *   name={['parameters', parameterType, fieldName]}
 *   parameterType={parameterType}
 *   fieldName={fieldName}
 *   onChange={(value) => handleParameterChange(fieldName, value)}
 *   isReadOnly={isReadOnly}
 *   disabled={isDisabled}
 * />
 * ```
 * 
 * BENEFITS:
 * - ✅ 90% less code
 * - ✅ Automatic validation by Ant Design
 * - ✅ Dynamic unit conversion still works
 * - ✅ Clear error messages with units
 * - ✅ Easy to debug and maintain
 * - ✅ Help text shows current constraints
 */
