# ESLint, <PERSON><PERSON><PERSON> & <PERSON> Quality Setup Guide

This document provides comprehensive instructions for setting up and configuring <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and lint-staged in your React project.

## Table of Contents

- [Overview](#overview)
- [Current Setup](#current-setup)
- [Installation Guide](#installation-guide)
- [Configuration Files](#configuration-files)
- [Scripts and Commands](#scripts-and-commands)
- [Git Hooks Setup](#git-hooks-setup)
- [IDE Integration](#ide-integration)
- [Troubleshooting](#troubleshooting)

## Overview

This project uses the following tools for code quality and consistency:

- **ESLint**: JavaScript/React linting and code quality
- **Prettier**: Code formatting
- **Husky**: Git hooks management
- **lint-staged**: Run linters on staged files only

## Current Setup

### Installed Dependencies

```json
{
  "devDependencies": {
    "eslint": "^9.31.0",
    "eslint-plugin-react": "^7.37.5",
    "eslint-plugin-react-refresh": "^0.4.19",
    "prettier": "^3.6.2",
    "husky": "^9.1.7",
    "lint-staged": "^16.1.2",
    "@eslint/js": "^9.31.0",
    "globals": "^16.3.0"
  }
}
```

### Package.json Scripts

```json
{
  "scripts": {
    "lint": "eslint .",
    "prepare": "husky"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": "eslint --fix"
  }
}
```

## Installation Guide

### 1. Install Core Dependencies

```bash
# Install ESLint and plugins
npm install --save-dev eslint @eslint/js globals
npm install --save-dev eslint-plugin-react eslint-plugin-react-refresh

# Install Prettier
npm install --save-dev prettier

# Install Git hooks tools
npm install --save-dev husky lint-staged
```

### 2. Initialize Husky

```bash
# Initialize husky (creates .husky directory)
npx husky init

# Install git hooks
npm run prepare
```

## Configuration Files

### ESLint Configuration (eslint.config.js)

```javascript
import js from '@eslint/js';
import globals from 'globals';
import pluginReact from 'eslint-plugin-react';
import { defineConfig } from 'eslint/config';

export default defineConfig([
  {
    files: ['**/*.{js,mjs,cjs,jsx}'],
    plugins: { js },
    extends: ['js/recommended'],
  },
  {
    files: ['**/*.{js,mjs,cjs,jsx}'],
    languageOptions: { globals: globals.browser },
  },
  pluginReact.configs.flat.recommended,
  {
    rules: {
      // Custom rules can be added here
      'react/prop-types': 'warn',
      'react/react-in-jsx-scope': 'off', // Not needed in React 17+
      'no-unused-vars': 'warn',
      'no-console': 'warn',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
]);
```

### Prettier Configuration (.prettierrc.json)

Create this file in your project root:

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### Prettier Ignore (.prettierignore)

Create this file to exclude certain files from formatting:

```
# Dependencies
node_modules/

# Build outputs
dist/
build/

# Generated files
*.min.js
*.min.css

# Package files
package-lock.json
yarn.lock
```

### ESLint Ignore (.eslintignore)

Create this file to exclude files from linting:

```
# Dependencies
node_modules/

# Build outputs
dist/
build/

# Configuration files
vite.config.js
*.config.js

# Generated files
*.min.js
```

## Scripts and Commands

### Add these scripts to package.json:

```json
{
  "scripts": {
    "lint": "eslint .",
    "lint:fix": "eslint . --fix",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "prepare": "husky"
  }
}
```

### Command Usage

```bash
# Lint all files
npm run lint

# Lint and auto-fix issues
npm run lint:fix

# Format all files with Prettier
npm run format

# Check if files are formatted correctly
npm run format:check
```

## Git Hooks Setup

### Pre-commit Hook (.husky/pre-commit)

```bash
npx lint-staged
```

### Lint-staged Configuration (in package.json)

```json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"],
    "*.{json,css,md}": ["prettier --write"]
  }
}
```

### Alternative: Separate lint-staged config file (.lintstagedrc.json)

```json
{
  "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"],
  "*.{json,css,md}": ["prettier --write"]
}
```

## IDE Integration

### VS Code Settings (.vscode/settings.json)

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ]
}
```

### Recommended VS Code Extensions

- ESLint (dbaeumer.vscode-eslint)
- Prettier - Code formatter (esbenp.prettier-vscode)
- Auto Rename Tag (formulahendry.auto-rename-tag)
- Bracket Pair Colorizer (coenraads.bracket-pair-colorizer)

## Troubleshooting

### Common Issues and Solutions

#### 1. "Unknown command: lint-staged"

**Problem**: Pre-commit hook fails with this error.
**Solution**: Use `npx lint-staged` instead of `npm lint-staged` in the pre-commit hook.

#### 2. ESLint and Prettier conflicts

**Problem**: ESLint and Prettier rules conflict.
**Solution**: Install eslint-config-prettier to disable conflicting rules:

```bash
npm install --save-dev eslint-config-prettier
```

#### 3. Files not being formatted on commit

**Problem**: lint-staged not running or not formatting files.
**Solution**:

- Check that husky is properly initialized
- Verify lint-staged configuration
- Ensure files are staged before committing

#### 4. ESLint errors in React components

**Problem**: React-specific linting errors.
**Solution**: Ensure React plugin is properly configured and React version is detected.

### Debugging Commands

```bash
# Test lint-staged manually
npx lint-staged

# Check ESLint configuration
npx eslint --print-config src/App.jsx

# Test Prettier on specific files
npx prettier --check src/**/*.{js,jsx}

# Check Husky hooks
ls -la .husky/
```

## Best Practices

1. **Commit frequently**: Small, frequent commits make it easier to identify issues.
2. **Fix linting errors**: Don't ignore ESLint warnings; they often catch real issues.
3. **Use consistent formatting**: Let Prettier handle formatting automatically.
4. **Review pre-commit changes**: Always review what lint-staged changes before committing.
5. **Keep configurations simple**: Start with basic rules and add complexity as needed.

## Additional Tools (Optional)

### TypeScript Support

If using TypeScript, add these dependencies:

```bash
npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin
```

### Commitlint (for commit message linting)

```bash
npm install --save-dev @commitlint/cli @commitlint/config-conventional
```

### EditorConfig (.editorconfig)

```ini
root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

[*.md]
trim_trailing_whitespace = false
```
