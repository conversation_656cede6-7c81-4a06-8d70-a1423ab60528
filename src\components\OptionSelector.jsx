import PropTypes from 'prop-types';

/**
 * OptionSelector Component
 * A reusable component that follows the same design pattern as ButtonGroup
 * Provides single selection from multiple options with consistent styling
 * 
 * @param {Array} options - Array of option objects with value and label
 * @param {string} selectedOption - Currently selected option value
 * @param {Function} onSelect - Callback function when option is selected
 * @param {string} className - Additional CSS classes
 * @param {boolean} disabled - Whether the selector is disabled
 * @param {string} size - Size variant ('small', 'medium', 'large')
 */
export default function OptionSelector({ 
  options, 
  selectedOption, 
  onSelect, 
  className = '',
  disabled = false,
  size = 'medium',
  style = {}
}) {
  const handleOptionClick = (optionValue) => {
    if (!disabled && onSelect) {
      onSelect(optionValue);
    }
  };

  const sizeClass = `option-selector-${size}`;
  const disabledClass = disabled ? 'option-selector-disabled' : '';

  return (
    <div 
      className={`option-selector ${sizeClass} ${disabledClass} ${className}`.trim()}
      role="radiogroup"
      aria-disabled={disabled}
      style={style}
    >
      {options.map((option, index) => (
        <button
          key={option.value}
          type="button"
          className={`option-selector-item ${selectedOption === option.value ? 'selected' : ''}`}
          onClick={() => handleOptionClick(option.value)}
          disabled={disabled}
          role="radio"
          aria-checked={selectedOption === option.value}
          aria-label={option.label}
          tabIndex={selectedOption === option.value ? 0 : -1}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
}

OptionSelector.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired
    })
  ).isRequired,
  selectedOption: PropTypes.string.isRequired,
  onSelect: PropTypes.func.isRequired,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large'])
};

OptionSelector.defaultProps = {
  className: '',
  disabled: false,
  size: 'medium'
};

// Named export for flexibility
export { OptionSelector };
