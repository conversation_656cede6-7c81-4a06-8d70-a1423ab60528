/**
 * Unit Conversion Test
 * Test the flow unit conversion from m³/h to m³/d
 */

import { 
  convertFlow, 
  convertFlowBetweenUnits, 
  getUnitLabel, 
  UNIT_SYSTEMS,
  CONVERSION_FACTORS 
} from '../utils/unitConversion.js';

// Test function to verify conversions
export function testFlowConversions() {
  console.log('=== Flow Unit Conversion Tests ===');
  
  // Test 1: Basic conversion factors
  console.log('\n1. Testing conversion factors:');
  console.log('m3h_to_m3d factor:', CONVERSION_FACTORS.FLOW.m3h_to_m3d); // Should be 24
  console.log('m3d_to_gpd factor:', CONVERSION_FACTORS.FLOW.m3d_to_gpd); // Should be 264.172...
  
  // Test 2: Unit labels
  console.log('\n2. Testing unit labels:');
  console.log('Metric flow unit:', getUnitLabel('flow', UNIT_SYSTEMS.METRIC)); // Should be 'm³/d'
  console.log('US flow unit:', getUnitLabel('flow', UNIT_SYSTEMS.US)); // Should be 'gpd'
  
  // Test 3: Flow conversions between specific units
  console.log('\n3. Testing specific unit conversions:');
  
  // Convert 100 m³/h to m³/d (should be 2400)
  const m3h_to_m3d = convertFlowBetweenUnits(100, 'm³/h', 'm³/d');
  console.log('100 m³/h to m³/d:', m3h_to_m3d, '(expected: 2400)');
  
  // Convert 2400 m³/d to m³/h (should be 100)
  const m3d_to_m3h = convertFlowBetweenUnits(2400, 'm³/d', 'm³/h');
  console.log('2400 m³/d to m³/h:', m3d_to_m3h, '(expected: 100)');
  
  // Convert 100 m³/d to gpd (should be ~26,417)
  const m3d_to_gpd = convertFlowBetweenUnits(100, 'm³/d', 'gpd');
  console.log('100 m³/d to gpd:', m3d_to_gpd, '(expected: ~26,417)');
  
  // Convert 26417 gpd to m³/d (should be ~100)
  const gpd_to_m3d = convertFlowBetweenUnits(26417, 'gpd', 'm³/d');
  console.log('26417 gpd to m³/d:', gpd_to_m3d, '(expected: ~100)');
  
  // Test 4: System-level conversions
  console.log('\n4. Testing system-level conversions:');
  
  // Convert 1000 from Metric to US (m³/d to gpd)
  const metric_to_us = convertFlow(1000, UNIT_SYSTEMS.METRIC, UNIT_SYSTEMS.US);
  console.log('1000 (Metric m³/d to US gpd):', metric_to_us, '(expected: ~264,172)');
  
  // Convert 264172 from US to Metric (gpd to m³/d)
  const us_to_metric = convertFlow(264172, UNIT_SYSTEMS.US, UNIT_SYSTEMS.METRIC);
  console.log('264172 (US gpd to Metric m³/d):', us_to_metric, '(expected: ~1000)');
  
  console.log('\n=== Test Complete ===');
}

// Test validation limits
export function testValidationLimits() {
  console.log('\n=== Validation Limits Test ===');
  
  // Import validation functions
  import('../config/formFieldConfig.js').then(({ getFormFieldValidation }) => {
    import('../config/parameterConfig.js').then(({ getParameterFieldValidation }) => {
      
      console.log('\n1. Form field validation (Feed Flow):');
      const formValidation = getFormFieldValidation('feedFlow', UNIT_SYSTEMS.METRIC);
      console.log('Metric feed flow limits:', formValidation);
      
      console.log('\n2. Parameter field validation (Q Perm):');
      const paramValidation = getParameterFieldValidation('qPerm', 'start', UNIT_SYSTEMS.METRIC);
      console.log('Metric Q Perm start limits:', paramValidation);
      
    });
  });
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  window.testFlowConversions = testFlowConversions;
  window.testValidationLimits = testValidationLimits;
  
  console.log('Unit conversion test functions loaded. Run:');
  console.log('- testFlowConversions() to test conversions');
  console.log('- testValidationLimits() to test validation limits');
}

export default {
  testFlowConversions,
  testValidationLimits
};
