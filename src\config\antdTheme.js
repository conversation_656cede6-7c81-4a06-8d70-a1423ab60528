/**
 * Ant Design Theme Configuration
 * Centralizes theme settings for consistent styling across all Ant Design components
 */

export const antdTheme = {
  token: {
    // Primary Colors
    colorPrimary: '#128370',        // Your brand color (--brand-500)
    colorPrimaryHover: '#0f6b5e',   // Darker shade for hover
    colorPrimaryActive: '#0a5248',  // Even darker for active state
    
    // Text Colors
    colorText: '#101828',           // Primary text (--gray-900)
    colorTextSecondary: '#667085',  // Secondary text (--gray-500)
    colorTextTertiary: '#98a2b3',   // Tertiary text (--gray-400)
    colorTextDisabled: '#667085',   // Disabled text
    
    // Background Colors
    colorBgContainer: '#ffffff',    // Container background (--white)
    colorBgElevated: '#ffffff',     // Elevated background
    colorBgLayout: '#fcfcfd',       // Layout background (--gray-25)
    colorBgSpotlight: '#f2f4f7',    // Spotlight background (--gray-100)
    
    // Border Colors
    colorBorder: '#e1e1e1',         // Default border (--gray-e1)
    colorBorderSecondary: '#e4e7ec', // Secondary border (--gray-200)
    
    // Typography
    fontFamily: "'Diodrum', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    fontSize: 14,                   // Base font size (--font-size-base)
    fontSizeSM: 12,                 // Small font size (--font-size-sm)
    fontSizeXS: 11,                 // Extra small font size (--font-size-xs)
    fontSizeLG: 16,                 // Large font size (--font-size-lg)
    fontWeightStrong: 500,          // Strong font weight
    
    // Border Radius
    borderRadius: 0,                // No border radius for inputs
    borderRadiusLG: 4,              // Large border radius for containers
    borderRadiusSM: 2,              // Small border radius
    
    // Control Heights
    controlHeight: 32,              // Standard input height
    controlHeightSM: 24,            // Small control height
    controlHeightLG: 40,            // Large control height
    
    // Spacing
    padding: 8,                     // Default padding
    paddingSM: 8,                   // Small padding
    paddingXS: 4,                   // Extra small padding
    paddingLG: 16,                  // Large padding
    
    // Line Heights
    lineHeight: 1.5,                // Normal line height (--line-height-normal)
    lineHeightSM: 1.25,             // Tight line height (--line-height-tight)
    lineHeightLG: 1.75,             // Relaxed line height (--line-height-relaxed)
    
    // Box Shadow
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    boxShadowSecondary: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
  },
  
  components: {
    // Input Components
    Input: {
      colorBgContainer: '#ffffff',
      controlHeight: 32,
      paddingInline: 8,
      borderRadius: 0,
      activeBorderColor: '#128370',
      hoverBorderColor: '#128370',
      activeShadow: 'none',
      errorActiveShadow: '0 0 0 2px rgba(255, 77, 79, 0.2)',
      warningActiveShadow: '0 0 0 2px rgba(255, 193, 7, 0.2)',
    },
    
    InputNumber: {
      colorBgContainer: '#ffffff',
      controlHeight: 32,
      paddingInline: 8,
      borderRadius: 0,
      activeBorderColor: '#128370',
      hoverBorderColor: '#128370',
      activeShadow: 'none',
      handleVisible: true,
    },
    
    // Date Picker
    DatePicker: {
      controlHeight: 32,
      borderRadius: 0,
      activeBorderColor: '#128370',
      hoverBorderColor: '#128370',
      activeShadow: 'none',
    },
    
    // Form Components
    Form: {
      labelFontSize: 12,
      labelColor: '#344054',          // --gray-700
      labelRequiredMarkColor: '#f04438', // Error red
      itemMarginBottom: 16,
      verticalLabelPadding: '0 0 4px',
    },
    
    // Button Components
    Button: {
      controlHeight: 32,
      borderRadius: 4,
      fontWeight: 500,
      primaryShadow: 'none',
      defaultShadow: 'none',
      dangerShadow: 'none',
    },
    
    // Select Components
    Select: {
      controlHeight: 32,
      borderRadius: 0,
      activeBorderColor: '#128370',
      hoverBorderColor: '#128370',
      activeShadow: 'none',
    },
    
    // Table Components
    Table: {
      headerBg: '#f2f4f7',           // --gray-100
      headerColor: '#344054',        // --gray-700
      borderColor: '#e4e7ec',        // --gray-200
      rowHoverBg: '#f9fafb',
    },
    
    // Card Components
    Card: {
      headerBg: 'transparent',
      colorBorderSecondary: '#e4e7ec', // --gray-200
      paddingLG: 24,
    },
    
    // Modal Components
    Modal: {
      borderRadiusLG: 8,
      paddingContentHorizontalLG: 24,
      paddingMD: 20,
    },
    
    // Message Components
    Message: {
      borderRadiusLG: 6,
    },
    
    // Notification Components
    Notification: {
      borderRadiusLG: 8,
    },
    
    // Tooltip Components
    Tooltip: {
      borderRadius: 4,
      colorBgSpotlight: '#1d2939',    // --gray-800
      colorTextLightSolid: '#ffffff',
    },
    
    // Divider Components
    Divider: {
      colorSplit: '#e4e7ec',          // --gray-200
    },
    
    // Typography Components
    Typography: {
      titleMarginBottom: '0.5em',
      titleMarginTop: '1.2em',
    },
  },
  
  // Algorithm for generating additional colors
  algorithm: undefined, // Use default algorithm
};

export default antdTheme;
