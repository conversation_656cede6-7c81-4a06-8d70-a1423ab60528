# RoAdvisorLite Quick Reference Guide

## Essential Commands

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run format       # Format code with Prettier
```

### Package Management
```bash
npm install <package>              # Add dependency
npm install --save-dev <package>   # Add dev dependency
npm uninstall <package>            # Remove dependency
npm update                         # Update dependencies
```

## Component Template

```javascript
import { useState, useEffect, useMemo } from 'react';
import { Input, Form } from 'antd';
import PropTypes from 'prop-types';
import { useFormData } from '../contexts/useFormContext';

export default function MyComponent({ 
  name, 
  label, 
  onChange, 
  disabled = false,
  ...props 
}) {
  const { unitSystem, customUnits } = useFormData();
  
  // Hooks and state
  const [localState, setLocalState] = useState('');
  
  // Computed values
  const computedValue = useMemo(() => {
    // Expensive calculation
    return someCalculation(localState);
  }, [localState]);
  
  // Event handlers
  const handleChange = (value) => {
    setLocalState(value);
    onChange?.(value);
  };
  
  return (
    <Form.Item label={label}>
      <Input
        value={localState}
        onChange={handleChange}
        disabled={disabled}
        {...props}
      />
    </Form.Item>
  );
}

MyComponent.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};
```

## Form Context Usage

### Access Form Data
```javascript
import { useFormData } from '../contexts/useFormContext';

const { formData, parameters, unitSystem, customUnits } = useFormData();
const temperature = formData.start.temperature;
```

### Update Form Data
```javascript
import { useFormActions } from '../contexts/useFormContext';

const { updateFormField, updateParameter } = useFormActions();

// Update form field
updateFormField('start', 'temperature', '25');

// Update parameter
updateParameter('feedPressure', '10');
```

### Validation
```javascript
import { useFormValidation } from '../contexts/useFormContext';

const { validateField, errors } = useFormValidation();

const result = validateField('temperature', '25', 'temperature');
const hasError = errors.temperature;
```

## Validation Patterns

### NumericInput with Validation
```javascript
<NumericInput
  name="temperature"
  label="Temperature"
  onChange={handleChange}
  parameterType="temperature"
  fieldName="startTemperature"
  required
/>
```

### Custom Validation
```javascript
import { validateWithRules, createValidationRule } from '../utils/validation';

const rules = [
  createValidationRule('required', 'Temperature is required'),
  createValidationRule('number', 'Must be a valid number'),
  createValidationRule('range', 'Must be between 0-100°C', { min: 0, max: 100 })
];

const result = validateWithRules(value, rules);
```

## Unit Conversion

### Get Unit Label
```javascript
import { getUnitLabel } from '../utils/unitConversion';

const unitLabel = getUnitLabel('pressure', unitSystem, customUnits);
// Returns: 'bar', 'psi', or custom unit
```

### Convert Values
```javascript
import { convertValue } from '../utils/unitConversion';

const convertedValue = convertValue(
  10,           // value
  'pressure',   // parameter type
  'Metric',     // from system
  'US',         // to system
  {},           // from custom units
  {}            // to custom units
);
```

## Styling Quick Reference

### CSS Variables
```css
/* Colors */
--brand-500: #1890ff;
--gray-500: #8c8c8c;
--white: #ffffff;

/* Spacing */
--spacing-sm: 8px;
--spacing-md: 16px;
--spacing-lg: 24px;

/* Typography */
--font-size-base: 14px;
```

### Component Dimensions
```css
/* Standard input dimensions */
.input-field {
  width: 200px;
  height: 32px;
}
```

### Ant Design Grid
```javascript
import { Row, Col } from 'antd';

<Row gutter={16}>
  <Col xs={24} sm={12} md={8} lg={6}>
    Content
  </Col>
</Row>
```

## Common Patterns

### Custom Units Modal Integration
```javascript
import { useCustomUnits } from '../hooks/useCustomUnits';

const {
  customUnits,
  isCustomUnitsModalVisible,
  showCustomUnitsModal,
  handleCustomUnitsSave,
  handleCustomUnitsCancel
} = useCustomUnits();
```

### Parameter Configuration
```javascript
import { getParameterFieldValidation } from '../config/parameterConfig';

const validation = getParameterFieldValidation(
  'pressure',     // parameter type
  'feedPressure', // field name
  unitSystem,     // current unit system
  customUnits     // custom units
);
```

### Status Determination
```javascript
import { determineStatus } from '../utils/statusUtils';

const status = determineStatus(currentValue, thresholdValue);
// Returns: 'good', 'warning', 'critical'
```

## Error Handling

### Try-Catch Pattern
```javascript
try {
  const result = riskyOperation();
  return result;
} catch (error) {
  console.error('Operation failed:', error);
  return defaultValue;
}
```

### Validation Error Display
```javascript
<Form.Item
  label="Temperature"
  validateStatus={hasError ? 'error' : ''}
  help={hasError ? errorMessage : helpText}
>
  <Input />
</Form.Item>
```

## Testing Checklist

### Component Testing
- [ ] Component renders without errors
- [ ] PropTypes validation works
- [ ] Event handlers function correctly
- [ ] Accessibility features work
- [ ] Responsive design functions

### Integration Testing
- [ ] Form context integration works
- [ ] Custom units integration works
- [ ] Validation behaves correctly
- [ ] Unit conversion is accurate

## Debugging Tips

### React DevTools
- Use React DevTools to inspect component state
- Check context values in provider components
- Monitor re-renders and performance

### Console Debugging
```javascript
// Debug form context
console.log('Form Data:', formData);
console.log('Unit System:', unitSystem);
console.log('Custom Units:', customUnits);

// Debug validation
console.log('Validation Result:', validationResult);
console.log('Errors:', errors);
```

### Common Issues
1. **Missing PropTypes**: Add PropTypes to all components
2. **Context not found**: Ensure component is wrapped in FormProvider
3. **Validation not working**: Check parameter type and field name
4. **Unit conversion errors**: Verify conversion factors

## File Structure Reference

```
src/
├── components/
│   ├── ButtonGroup.jsx
│   ├── NumericInput.jsx
│   ├── ParameterTable/
│   └── DateInput/
├── contexts/
│   ├── FormProvider.jsx
│   ├── FormContext.jsx
│   └── useFormContext.js
├── hooks/
│   ├── useCustomUnits.js
│   └── useValidation.js
├── config/
│   ├── parameterConfig.js
│   └── formFieldConfig.js
├── utils/
│   ├── validation/
│   ├── unitConversion.js
│   └── calculations/
└── constants/
    └── index.js
```

## Git Workflow

### Commit Message Format
```
type(scope): description

feat(components): add new NumericInput component
fix(validation): correct unit conversion for pressure
docs(readme): update installation instructions
```

### Pre-commit Checklist
- [ ] Code passes ESLint
- [ ] Code is formatted with Prettier
- [ ] PropTypes are included
- [ ] Tests pass
- [ ] Documentation is updated

---

*Keep this guide handy for quick reference during development!*
