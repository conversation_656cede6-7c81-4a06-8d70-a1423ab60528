# RoAdvisorLite AI Agent Instructions

## Project Overview

RoAdvisorLite is a React-based web application for Reverse Osmosis (RO) membrane performance analysis and forecasting. Built with React 19, Vite, and Ant Design, it features:

- Centralized form state management
- Complex validation rules including cross-field validation
- Dynamic unit conversion system
- Comprehensive error handling

## Key Architectural Patterns

### Form State Management

- Uses context-based form management (`src/contexts/FormContext.jsx`)
- All form data accessed through `useFormData` hook
- State updates via `useFormActions` hook
- Example:

```javascript
const { formData, parameters } = useFormData();
const { updateFormField } = useFormActions();
updateFormField('start', 'temperature', '25');
```

### Validation System

- Multi-layered validation in `src/utils/validation/`
- Field-level validation in `NumericInput` component
- Cross-field validation (e.g., feedConductivity must be greater than permeate)
- Validation rules defined in `parameterConfig.js` and `formFieldConfig.js`

### Unit Conversion

- Centralized unit conversion in `src/utils/unitConversion.js`
- Dynamic unit system switching (US/Metric)
- Custom unit support through `useCustomUnits` hook

## Development Workflow

### Setup and Running

```bash
npm install          # Install dependencies
npm run dev         # Start dev server
npm run build       # Production build
npm run lint:fix    # Fix linting issues
npm run format      # Format code
```

### Required Tools

- Node.js 18+
- VS Code with ESLint and Prettier extensions
- Git with Husky hooks enabled

### Code Quality Gates

All commits must pass:

1. ESLint validation
2. Prettier formatting
3. PropTypes validation
4. Unit tests (when added)

## Common Tasks

### Adding New Form Fields

1. Define field config in `src/config/formFieldConfig.js`
2. Add validation rules if needed
3. Use `NumericInput` or create specialized component
4. Update form context if needed

### Implementing Validation

1. Use `createValidationRule` for field rules
2. Add cross-field validation in component if needed
3. Update validation messages in `validationMessages.js`

### Unit Conversion Implementation

1. Add conversion factors in `unitConversion.js`
2. Use `convertValue` helper for conversions
3. Update unit labels in configuration

## Critical Files

- `src/components/NumericInput.jsx` - Core input component
- `src/contexts/FormContext.jsx` - Form state management
- `src/utils/validation/` - Validation system
- `src/utils/unitConversion.js` - Unit conversion logic

## Common Pitfalls

1. Always handle all unit conversion cases
2. Include cross-field validation where needed
3. Use proper error message formatting
4. Handle empty/null values appropriately

## Testing Focus Areas

- Unit conversion accuracy
- Validation rule completeness
- Cross-field validation logic
- Error message clarity

---

_Update this guide when adding major features or changing architectural patterns._
